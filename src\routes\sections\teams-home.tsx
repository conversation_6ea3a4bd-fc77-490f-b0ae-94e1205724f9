import { lazy } from 'react';

import { CONFIG } from 'src/config-global';
import { SimpleTeamsLayout } from 'src/layouts/simple-teams';

import { AuthGuard } from 'src/auth/guard';

// ----------------------------------------------------------------------

const TeamsHomePage = lazy(() => import('src/pages/teams-home'));

// ----------------------------------------------------------------------

export const teamsHomeRoutes = [
  {
    path: '/',
    element: CONFIG.auth.skip ? (
      <SimpleTeamsLayout>
        <TeamsHomePage />
      </SimpleTeamsLayout>
    ) : (
      <AuthGuard>
        <SimpleTeamsLayout>
          <TeamsHomePage />
        </SimpleTeamsLayout>
      </AuthGuard>
    ),
  },
];
