import { lazy } from 'react';

import { CONFIG } from 'src/config-global';

import { AuthGuard } from 'src/auth/guard';
import { SidebarOnlyLayout } from 'src/layouts/sidebar-only';

// ----------------------------------------------------------------------

const TeamsHomePage = lazy(() => import('src/pages/teams-home'));

// ----------------------------------------------------------------------

export const teamsHomeRoutes = [
  {
    path: '/',
    element: CONFIG.auth.skip ? (
      <SidebarOnlyLayout>
        <TeamsHomePage />
      </SidebarOnlyLayout>
    ) : (
      <AuthGuard>
        <SidebarOnlyLayout>
          <TeamsHomePage />
        </SidebarOnlyLayout>
      </AuthGuard>
    ),
  },
];
