import { lazy } from 'react';
import { Outlet } from 'react-router-dom';

import { CONFIG } from 'src/config-global';
import { ProfileLayout } from 'src/layouts/profile/layout';
import { SidebarOnlyLayout } from 'src/layouts/sidebar-only';

import { AuthGuard } from 'src/auth/guard';
import { paths } from '../paths';

// ----------------------------------------------------------------------

const IndexPage = lazy(() => import('src/pages/dashboard/one'));
const AgentsPage = lazy(() => import('src/pages/dashboard/agents-page'));
const TeamsPage = lazy(() => import('src/pages/dashboard/teams/teams-page'));
const UseTemplatePage = lazy(() => import('src/pages/dashboard/teams/use-template-page'));
const RecentlyUsedPage = lazy(() => import('src/pages/dashboard/teams/recently-used-page'));
const ChatPage = lazy(() => import('src/pages/dashboard/teams/chat-page'));
const ProfilePage = lazy(() => import('src/pages/dashboard/profile/profile-page'));
const KnowledgeBasePage = lazy(() => import('src/pages/dashboard/profile/knowledge-base-page'));
const SettingsPage = lazy(() => import('src/pages/dashboard/profile/settings-page'));
// const PageTwo = lazy(() => import('src/pages/dashboard/two'));

// ----------------------------------------------------------------------

const layoutContent = (
  <SidebarOnlyLayout>
    <Outlet />
  </SidebarOnlyLayout>
);

export const dashboardRoutes = [
  // Chat page route with no layout
  {
    path: 'dashboard/teams/chat/:id',
    element: CONFIG.auth.skip ? (
      <ChatPage />
    ) : (
      <AuthGuard>
        <ChatPage />
      </AuthGuard>
    ),
  },
  {
    path: 'dashboard',
    element: CONFIG.auth.skip ? <>{layoutContent}</> : <AuthGuard>{layoutContent}</AuthGuard>,
    children: [
      { element: <IndexPage />, index: true },
      { path: 'agents', element: <AgentsPage /> },
      { path: 'teams', element: <TeamsPage /> },
      { path: 'teams/recently-used', element: <RecentlyUsedPage /> },
      { path: 'teams/use-template/:id', element: <UseTemplatePage /> },

      // { path: 'two', element: <PageTwo /> },
      // { path: 'three', element: <PageThree /> },
      // {
      //   path: 'group',
      //   children: [
      //     { element: <PageFour />, index: true },
      //     { path: 'five', element: <PageFive /> },
      //     { path: 'six', element: <PageSix /> },
      //   ],
      // },
    ],
  },
  // Knowledge Base page with no sidebar
  {
    path: 'dashboard/profile/knowledge-base',
    element: CONFIG.auth.skip ? (
      <SidebarOnlyLayout>
        <KnowledgeBasePage />
      </SidebarOnlyLayout>
    ) : (
      <AuthGuard>
        <SidebarOnlyLayout>
          <KnowledgeBasePage />
        </SidebarOnlyLayout>
      </AuthGuard>
    ),
  },
  // Profile pages with sidebar
  {
    path: 'dashboard/profile',
    element: CONFIG.auth.skip ? (
      <ProfileLayout>
        <Outlet />
      </ProfileLayout>
    ) : (
      <AuthGuard>
        <ProfileLayout>
          <Outlet />
        </ProfileLayout>
      </AuthGuard>
    ),
    children: [
      { element: <ProfilePage />, index: true },
      { path: 'settings', element: <SettingsPage /> },
    ],
  },
];
