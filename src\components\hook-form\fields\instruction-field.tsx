import { useState, useRef } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import {
  TextField,
  Box,
  Typography,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ClickAwayListener,
} from '@mui/material';
import { Agent, MOCK_AGENTS } from 'src/sections/teams/form/use-team-template-form';

// ----------------------------------------------------------------------

interface InstructionFieldProps {
  name: string;
  label?: string;
  placeholder?: string;
  helperText?: React.ReactNode;
  multiline?: boolean;
  rows?: number;
  fullWidth?: boolean;
  required?: boolean;
  disabled?: boolean;
  sx?: object;
}

export function InstructionField({
  name,
  label,
  placeholder,
  helperText,
  multiline = true,
  rows = 4,
  fullWidth = true,
  required = false,
  disabled = false,
  sx,
  ...other
}: InstructionFieldProps) {
  const { control } = useFormContext();
  const [mentionAnchorEl, setMentionAnchorEl] = useState<null | HTMLElement>(null);
  const [cursorPosition, setCursorPosition] = useState(0);
  const textFieldRef = useRef<HTMLDivElement>(null);
  const [isFocused, setIsFocused] = useState(false);

  // Function to format text with highlighted curly braces content
  const formatText = (text: string) => {
    if (!text) return '';

    // Replace text within curly braces with highlighted spans
    const parts = text.split(/({[^}]*})|(@[\w]+ )/g).filter(Boolean);

    return parts.map((part, index) => {
      if (part.startsWith('{') && part.endsWith('}')) {
        // Extract content within curly braces
        const content = part.substring(1, part.length - 1);
        return (
          <span
            key={index}
            style={{
              backgroundColor: '#FFC9B2', // Light orange background
              color: '#D84315', // Dark orange text
              padding: '2px 4px',
              borderRadius: '4px',
              fontWeight: 'medium',
            }}
          >
            {content}
          </span>
        );
      }

      if (part.startsWith('@') && part.includes(' ')) {
        // Handle @ mentions
        const mentionName = part.substring(1, part.indexOf(' '));
        const restOfText = part.substring(part.indexOf(' '));

        // We could use the agent avatar here if needed

        return (
          <span key={index}>
            <span
              style={{
                backgroundColor: '#E3F2FD', // Light blue background
                color: '#1976D2', // Blue text
                padding: '2px 4px',
                borderRadius: '4px',
                fontWeight: 'medium',
              }}
            >
              @{mentionName}
            </span>
            {restOfText}
          </span>
        );
      }
      return part;
    });
  };

  // Handle text change and check for @ mentions
  const handleTextChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    onChange: (value: string) => void
  ) => {
    const value = event.target.value;
    onChange(value);

    // Get cursor position
    const cursorPos = event.target.selectionStart || 0;
    setCursorPosition(cursorPos);

    // Check if we're in a mention context
    const textBeforeCursor = value.substring(0, cursorPos);
    const mentionMatch = textBeforeCursor.match(/@([^\s@]*)$/);

    if (mentionMatch) {
      // We have a mention match
      setMentionAnchorEl(event.target);

      // We could filter agents based on query if needed
    } else {
      setMentionAnchorEl(null);
    }
  };

  // Handle selecting a mention
  const handleSelectMention = (agent: Agent, value: string, onChange: (value: string) => void) => {
    const textBeforeCursor = value.substring(0, cursorPosition);
    const mentionMatch = textBeforeCursor.match(/@([^\s@]*)$/);

    if (mentionMatch) {
      const startPos = textBeforeCursor.lastIndexOf('@');
      // Use the agent name with @ symbol
      const newText =
        value.substring(0, startPos) + `@${agent.name} ` + value.substring(cursorPosition);

      onChange(newText);
    } else {
      // If no match, just append the agent name at the cursor position
      const newText =
        value.substring(0, cursorPosition) + `@${agent.name} ` + value.substring(cursorPosition);
      onChange(newText);
    }

    setMentionAnchorEl(null);
  };

  // Close mention dropdown when clicking away
  const handleClickAway = () => {
    setMentionAnchorEl(null);
  };

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <ClickAwayListener onClickAway={handleClickAway}>
          <Box sx={{ position: 'relative', width: '100%' }}>
            <Typography variant="subtitle2" gutterBottom>
              {label}
            </Typography>

            {/* Single field with formatted text */}
            <Box
              sx={{
                border: '1px solid',
                borderColor: error ? 'error.main' : isFocused ? 'primary.main' : 'divider',
                borderRadius: 1,
                position: 'relative',
                minHeight: rows * 24,
                p: 0,
                '&:hover': {
                  borderColor: 'text.primary',
                },
                '&:focus-within': {
                  borderColor: 'primary.main',
                  boxShadow: '0 0 0 2px rgba(0, 171, 85, 0.2)',
                },
              }}
            >
              {/* Visible textarea with formatted text overlay */}
              <Box sx={{ position: 'relative', width: '100%', height: '100%' }}>
                <TextField
                  {...field}
                  ref={textFieldRef}
                  fullWidth
                  placeholder={placeholder}
                  error={!!error}
                  multiline
                  rows={rows}
                  disabled={disabled}
                  onChange={(e) => handleTextChange(e, field.onChange)}
                  onFocus={() => setIsFocused(true)}
                  onBlur={() => setIsFocused(false)}
                  sx={{
                    '& .MuiInputBase-root': {
                      padding: 0,
                      height: '100%',
                    },
                    '& .MuiInputBase-input': {
                      padding: '16px',
                      color: 'rgba(0,0,0,0.05)', // Very slight color to see text being typed
                      caretColor: '#00AB55', // Primary green color for cursor
                      textShadow: '0 0 0 transparent', // Remove any text shadow
                      position: 'relative',
                      zIndex: 2, // Higher z-index to ensure cursor is on top
                      fontSize: '1rem', // Match font size with the overlay text
                      lineHeight: 1.8, // Match line height with the overlay text
                      fontFamily: 'inherit',
                    },
                  }}
                  {...other}
                />

                {/* Formatted display overlay */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    p: '16px', // Match exactly with the input padding
                    pointerEvents: 'none', // Allow clicks to pass through to the input
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-word',
                    color: field.value ? 'text.primary' : 'text.secondary',
                    zIndex: 1, // Lower than the cursor but higher than the base
                    fontSize: '1rem', // Match with input
                    lineHeight: 1.8,
                    fontFamily: 'inherit',
                  }}
                >
                  {field.value ? (
                    <Box sx={{ lineHeight: 1.8 }}>{formatText(field.value)}</Box>
                  ) : (
                    <Box sx={{ color: 'text.secondary' }}>{placeholder}</Box>
                  )}
                </Box>
              </Box>

              {/* Agent menu popup when typing @ */}
              {Boolean(mentionAnchorEl) && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: '50%', // Center vertically
                    left: '50%', // Center horizontally
                    transform: 'translate(-50%, -50%)', // Perfect centering
                    width: 250,
                    maxHeight: 300,
                    bgcolor: 'background.paper',
                    boxShadow: 6,
                    borderRadius: 1,
                    overflow: 'auto',
                    zIndex: 20, // Higher z-index to be on top of everything
                    border: '1px solid',
                    borderColor: 'primary.main',
                  }}
                >
                  <List dense sx={{ p: 0 }}>
                    {MOCK_AGENTS.map((agent) => (
                      <ListItem
                        key={agent.id}
                        onClick={() => handleSelectMention(agent, field.value, field.onChange)}
                        sx={{
                          py: 0.5,
                          cursor: 'pointer',
                          '&:hover': {
                            bgcolor: 'action.hover',
                          },
                        }}
                      >
                        <ListItemAvatar sx={{ minWidth: 36 }}>
                          <Avatar
                            src={agent.avatar}
                            alt={agent.name}
                            sx={{ width: 24, height: 24 }}
                          />
                        </ListItemAvatar>
                        <ListItemText
                          primary={agent.name}
                          primaryTypographyProps={{
                            variant: 'body2',
                            fontWeight: 'medium',
                          }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </Box>

            {/* Error message */}
            {error && (
              <Typography color="error" variant="caption" sx={{ mt: 0.5, display: 'block' }}>
                {error.message}
              </Typography>
            )}
            {helperText && !error && (
              <Typography
                variant="caption"
                sx={{ mt: 0.5, display: 'block', color: 'text.secondary' }}
              >
                {helperText}
              </Typography>
            )}
          </Box>
        </ClickAwayListener>
      )}
    />
  );
}
