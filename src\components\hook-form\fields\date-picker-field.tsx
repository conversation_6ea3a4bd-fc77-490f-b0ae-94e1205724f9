import { Controller, useFormContext } from 'react-hook-form';
import { TextField } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';

// ----------------------------------------------------------------------

interface DatePickerFieldProps {
  name: string;
  label?: string;
  helperText?: React.ReactNode;
  fullWidth?: boolean;
  required?: boolean;
  disabled?: boolean;
  sx?: object;
}

export function DatePickerField({
  name,
  label,
  helperText,
  fullWidth = true,
  required = false,
  disabled = false,
  sx,
  ...other
}: DatePickerFieldProps) {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DatePicker
            label={label}
            value={field.value}
            onChange={(newValue) => {
              field.onChange(newValue);
            }}
            slotProps={{
              textField: {
                fullWidth,
                error: !!error,
                helperText: error ? error?.message : helperText,
                required,
                disabled,
                sx,
                ...other,
              },
            }}
          />
        </LocalizationProvider>
      )}
    />
  );
}
