import { z as zod } from 'zod';
import { useState, useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import InputAdornment from '@mui/material/InputAdornment';

import { AppButton } from 'src/components/common';

import { useRouter } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';

import { useBoolean } from 'src/hooks/use-boolean';
import { useTheme } from '@mui/material/styles';

import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';

import { CONFIG } from 'src/config-global';
import { RouterLink } from 'src/routes/components';
import { Card } from '@mui/material';

// ----------------------------------------------------------------------

export const NewPasswordSchema = zod
  .object({
    password: zod
      .string()
      .min(1, { message: 'Password is required!' })
      .min(8, { message: 'Password must be at least 8 characters!' })
      .regex(/[A-Z]/, { message: 'Password must include at least one uppercase letter!' })
      .regex(/[a-z]/, { message: 'Password must include at least one lowercase letter!' })
      .regex(/[0-9]/, { message: 'Password must include at least one number!' })
      .regex(/[^A-Za-z0-9\s]/, { message: 'Password must include at least one special character!' })
      .refine((value) => !value.startsWith(' ') && !value.endsWith(' '), {
        message: 'Password cannot have spaces at the beginning or end!',
      }),
    confirmPassword: zod.string().min(1, { message: 'Confirm password is required!' }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Passwords must match!',
    path: ['confirmPassword'],
  });

export type NewPasswordSchemaType = zod.infer<typeof NewPasswordSchema>;

// ----------------------------------------------------------------------

export function JwtNewPasswordView() {
  const router = useRouter();
  const [errorMsg, setErrorMsg] = useState('');
  const [successMsg, setSuccessMsg] = useState('');
  const theme = useTheme();
  const password = useBoolean();
  const confirmPassword = useBoolean();

  // Validation states
  const [validations, setValidations] = useState({
    minLength: false,
    hasUpperLower: false,
    hasNumber: false,
    hasSpecial: false,
    noSpaces: false,
    passwordsMatch: false,
  });

  const methods = useForm<NewPasswordSchemaType>({
    resolver: zodResolver(NewPasswordSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
    mode: 'onChange',
  });

  // Function to check password validations
  const checkPasswordValidation = (password: string, confirmPassword: string) => {
    setValidations({
      minLength: password.length >= 8,
      hasUpperLower: /[A-Z]/.test(password) && /[a-z]/.test(password),
      hasNumber: /[0-9]/.test(password),
      hasSpecial: /[^A-Za-z0-9\s]/.test(password),
      noSpaces: !password.startsWith(' ') && !password.endsWith(' '),
      passwordsMatch: password === confirmPassword && password !== '',
    });
  };

  // Watch for password and confirmPassword changes
  const watchPassword = methods.watch('password');
  const watchConfirmPassword = methods.watch('confirmPassword');

  // Update validations when passwords change
  useEffect(() => {
    checkPasswordValidation(watchPassword || '', watchConfirmPassword || '');
  }, [watchPassword, watchConfirmPassword]);

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (_data) => {
    try {
      // Here you would call your API to change the password
      // Password would be sent to backend
      // console.log('New password:', data.password);

      // Show success message
      setSuccessMsg('Password changed successfully!');
      setErrorMsg('');

      // Redirect to sign in page after a short delay
      setTimeout(() => {
        router.push(paths.auth.jwt.signIn);
      }, 2000);
    } catch (error) {
      console.error(error);
      setErrorMsg(error instanceof Error ? error.message : 'Something went wrong');
      setSuccessMsg('');
    }
  });

  return (
    <>
      <Box
        sx={{
          width: '100%',
          maxWidth: 465,
          height: '100%',
          mx: 'auto',
          bgcolor: 'rgba(244, 244, 244)',
          p: 4,
          borderRadius: 2,
          boxShadow: 3,
        }}
      >
        <Box justifyContent="center" sx={{ mb: 1 }} display="flex">
          <Box
            component="img"
            src={`${CONFIG.site.basePath}/logo/logo-single.svg`}
            sx={{ width: 'auto', height: 44 }}
          />
          <Typography fontWeight="bold" variant="h3" p="6px" lineHeight="32px">
            Workforces
          </Typography>
        </Box>
        <Box sx={{ my: 2 }} />
        <Link
          component={RouterLink}
          href={paths.auth.jwt.signIn}
          variant="body2"
          sx={{
            display: 'flex',
            alignItems: 'center',
            textDecoration: 'none',
            my: 3,
          }}
        >
          <Iconify
            icon="eva:arrow-back-fill"
            width={20}
            sx={{ mx: 2, color: 'rgba(125, 64, 217, 1)' }}
          />
          <Typography color="rgba(15, 14, 17, 1)">Back to Login</Typography>
        </Link>
        <Typography variant="h5" sx={{ fontWeight: 700 }}>
          Change password
        </Typography>
        <Typography variant="body1" sx={{ color: 'rgba(15, 14, 17, 1)', mb: 2 }}>
          Enter your new password to change it.{' '}
        </Typography>

        {!!successMsg && (
          <Alert severity="success" sx={{ mb: 4 }}>
            {successMsg}
          </Alert>
        )}

        {!!errorMsg && (
          <Alert severity="error" sx={{ mb: 4 }}>
            {errorMsg}
          </Alert>
        )}

        <Form methods={methods} onSubmit={onSubmit}>
          <Stack spacing={4} sx={{ width: '100%', gap: '20px' }}>
            <Card
              sx={{
                border: '1px solid rgba(224, 223, 226, 1)',
                borderRadius: '12px',
                p: 2,
                backgroundColor: 'white',
                display: 'flex',
                flexDirection: 'column',
                height: '80px',
                width: '100%',
              }}
            >
              <Controller
                name="password"
                defaultValue=""
                render={({ field }) => (
                  <Field.Text
                    {...field}
                    name="password"
                    label="New Password"
                    type={password.value ? 'text' : 'password'}
                    placeholder="••••••••••••••••••••••"
                    fullWidth
                    variant="standard"
                    InputProps={{
                      disableUnderline: true,
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton onClick={password.onToggle} edge="end">
                            <Iconify
                              sx={{
                                transform: 'translateY(-12px)',
                                color: 'rgba(144, 108, 229, 1)',
                              }}
                              icon={password.value ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                            />
                          </IconButton>
                        </InputAdornment>
                      ),
                      startAdornment: (
                        <InputAdornment position="start">
                          <Iconify
                            icon="mdi:key-outline"
                            width={30}
                            sx={{
                              mr: 2,
                              transform: 'translateY(-14px)',
                              color: 'rgba(144, 108, 229, 1)',
                            }}
                          />
                        </InputAdornment>
                      ),
                      sx: {
                        px: 1,

                        height: 35,
                        borderRadius: 2,
                      },
                    }}
                    InputLabelProps={{
                      style: {
                        fontSize: '1.1rem',
                        color: 'rgba(70, 70, 70, 1)',
                      },
                      sx: {
                        color: 'red',
                        px: 10,
                      },
                      shrink: true,
                    }}
                  />
                )}
              />
            </Card>

            <Box>
              <Stack spacing={1}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Iconify
                    icon={
                      validations.minLength ? 'eva:checkmark-circle-fill' : 'eva:close-circle-fill'
                    }
                    color={validations.minLength ? 'success.main' : 'error.main'}
                    width={16}
                  />
                  <Typography variant="body2">Minimum 8 characters long</Typography>
                </Stack>

                <Stack direction="row" alignItems="center" spacing={1}>
                  <Iconify
                    icon={
                      validations.hasUpperLower
                        ? 'eva:checkmark-circle-fill'
                        : 'eva:close-circle-fill'
                    }
                    color={validations.hasUpperLower ? 'success.main' : 'error.main'}
                    width={16}
                  />
                  <Typography variant="body2">
                    Must include uppercase, lowercase, and a number
                  </Typography>
                </Stack>

                <Stack direction="row" alignItems="center" spacing={1}>
                  <Iconify
                    icon={
                      validations.hasSpecial ? 'eva:checkmark-circle-fill' : 'eva:close-circle-fill'
                    }
                    color={validations.hasSpecial ? 'success.main' : 'error.main'}
                    width={16}
                  />
                  <Typography variant="body2">
                    Must contain at least one special character
                  </Typography>
                </Stack>

                <Stack direction="row" alignItems="center" spacing={1}>
                  <Iconify
                    icon={
                      validations.noSpaces ? 'eva:checkmark-circle-fill' : 'eva:close-circle-fill'
                    }
                    color={validations.noSpaces ? 'success.main' : 'error.main'}
                    width={16}
                  />
                  <Typography variant="body2">No spaces before or after the password</Typography>
                </Stack>
              </Stack>
            </Box>
            <Card
              sx={{
                border: '1px solid rgba(224, 223, 226, 1)',
                borderRadius: '12px',
                p: 2,
                backgroundColor: 'white',
                display: 'flex',
                flexDirection: 'column',
                height: '80px',
                width: '100%',
              }}
            >
              <Controller
                name="confirmPassword"
                defaultValue=""
                render={({ field }) => (
                  <Field.Text
                    {...field}
                    name="confirmPassword"
                    label="New Password"
                    type={password.value ? 'text' : 'password'}
                    placeholder="••••••••••••••••••••••"
                    fullWidth
                    variant="standard"
                    InputProps={{
                      disableUnderline: true,
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton onClick={password.onToggle} edge="end">
                            <Iconify
                              sx={{
                                transform: 'translateY(-12px)',
                                color: 'rgba(144, 108, 229, 1)',
                              }}
                              icon={password.value ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                            />
                          </IconButton>
                        </InputAdornment>
                      ),
                      startAdornment: (
                        <InputAdornment position="start">
                          <Iconify
                            icon="mdi:key-outline"
                            width={30}
                            sx={{
                              mr: 2,
                              transform: 'translateY(-14px)',
                              color: 'rgba(144, 108, 229, 1)',
                            }}
                          />
                        </InputAdornment>
                      ),
                      sx: {
                        px: 1,

                        height: 35,
                        borderRadius: 2,
                      },
                    }}
                    InputLabelProps={{
                      style: {
                        fontSize: '1.1rem',
                        color: 'rgba(70, 70, 70, 1)',
                      },
                      sx: {
                        color: 'red',
                        px: 10,
                      },
                      shrink: true,
                    }}
                  />
                )}
              />
            </Card>

            <AppButton
              label="Change Password"
              fullWidth
              sx={{
                backgroundColor: 'rgba(144, 108, 229, 1)',
                fontWeight: 300,
                color: '#fff',
                '&:hover': {
                  backgroundColor: 'rgba(120, 90, 200, 1)', // optional hover style
                },
              }}
              size="large"
              type="submit"
              variant="contained"
              isLoading={isSubmitting}
            />
          </Stack>
        </Form>
      </Box>
    </>
  );
}
