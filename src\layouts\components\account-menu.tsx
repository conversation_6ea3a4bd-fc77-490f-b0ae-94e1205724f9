import { useState, useCallback } from 'react';
import type { SxProps, Theme } from '@mui/material/styles';

import Box from '@mui/material/Box';
import Menu from '@mui/material/Menu';
import Avatar from '@mui/material/Avatar';
import Divider from '@mui/material/Divider';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';
import { signOut } from 'src/auth/context/jwt';
import { useMockedUser, useAuthContext } from 'src/auth/hooks';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

export type AccountMenuProps = {
  sx?: SxProps<Theme>;
  [key: string]: any;
};

export function AccountMenu({ sx, ...other }: AccountMenuProps) {
  const router = useRouter();
  const { user } = useMockedUser();
  const { checkUserSession } = useAuthContext();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleLogout = useCallback(async () => {
    try {
      await signOut();
      await checkUserSession?.();
      setAnchorEl(null); // Close the menu
      router.push(paths.auth.jwt.signIn);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error during logout:', error);
    }
  }, [checkUserSession, router, setAnchorEl]);

  const handleClickMenuItem = (path: string) => {
    handleClose();
    router.push(path);
  };

  return (
    <>
      <IconButton
        onClick={handleClick}
        sx={{
          p: 0,

          ...(open && {
            '&:before': {
              zIndex: 1,
              content: "''",
              width: '100%',
              height: '100%',
              borderRadius: '50%',
              position: 'absolute',
              bgcolor: (theme) => theme.palette.primary.lighter,
            },
          }),
          ...sx,
        }}
        {...other}
      >
        <Avatar
          src={user?.photoURL}
          alt={user?.displayName}
          sx={{
            width: 40,
            height: 40,
            border: (theme) => `solid 2px ${theme.palette.background.default}`,
          }}
        >
          {user?.displayName?.charAt(0).toUpperCase()}
        </Avatar>
      </IconButton>

      <Menu
        id="account-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        onClick={handleClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              width: 220,
              overflow: 'visible',
              mt: 1.5,
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.1))',
              '&:before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0,
              },
            },
          },
        }}
      >
        <Box sx={{ px: 2, py: 1.5 }}>
          <Typography variant="subtitle1" noWrap>
            {user?.displayName}
          </Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }} noWrap>
            {user?.email}
          </Typography>
        </Box>

        <Divider sx={{ borderStyle: 'dashed' }} />

        <MenuItem onClick={() => handleClickMenuItem(paths.dashboard.profile.root)} sx={{ m: 1 }}>
          <Iconify icon="mdi:account-outline" width={24} sx={{ mr: 2 }} />
          Profile
        </MenuItem>

        <MenuItem onClick={() => handleClickMenuItem(paths.dashboard.root)} sx={{ m: 1 }}>
          <Iconify icon="mdi:view-dashboard-outline" width={24} sx={{ mr: 2 }} />
          Dashboard
        </MenuItem>

        <Divider sx={{ borderStyle: 'dashed' }} />

        <MenuItem onClick={handleLogout} sx={{ m: 1 }}>
          <Iconify icon="mdi:logout-variant" width={24} sx={{ mr: 2 }} />
          Logout
        </MenuItem>
      </Menu>
    </>
  );
}
