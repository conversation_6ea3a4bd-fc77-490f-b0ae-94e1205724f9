import { styled } from '@mui/material/styles';
import { Card, CardProps } from '@mui/material';

// Styled Card component with cursor pointer and primary color border on hover
export const StyledTeamCard = styled(Card)<CardProps>(({ theme }) => ({
  padding: theme.spacing(3),
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'all 0.2s ease-in-out',
  cursor: 'pointer', // Use pointer cursor
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
  },
  '&:hover': {
    boxShadow: theme.customShadows.z16,
    '&::after': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: 'inherit',
      border: `2px solid ${theme.palette.primary.main}`,
      pointerEvents: 'none',
    },
    '& .MuiInputBase-root': {
      caretColor: theme.palette.primary.main,
    },
  },
  // Make sure content is above the pseudo-elements
  '& > *': {
    position: 'relative',
    zIndex: 1,
  },
}));

// No custom cursor attributes needed

export default StyledTeamCard;
