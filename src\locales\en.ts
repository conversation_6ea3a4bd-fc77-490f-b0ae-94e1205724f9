export const en = {
  dialogs: {
    confirmDeleteMessage: 'are you sure',
    alert: 'Alert',
    info: 'Info',
    success: 'Success',
    warning: 'Warning',
    error: 'Error',
  },
  buttons: {
    delete: 'Delete',
    cancel: 'Cancel',
    enabled: 'Enabled',
    disabled: 'Disabled',
    upload: 'Upload',
    clear: 'Clear',
    submit: 'Submit',
    save: 'Save',
    signOut: 'Sign Out',
    connect: 'Connect',
    viewAll: 'View All',
    createNew: 'Create New',
    addNew: 'Add New',
    search: 'Search',
    goBack: 'Go Back',
    continue: 'Continue',
    close: 'Close',
    edit: 'Edit',
    remove: 'Remove',
    update: 'Update',
    next: 'Next',
    previous: 'Previous',
    finish: 'Finish',
  },
  tables: {
    rowsPerPageLabel: 'Rows per page',
    dense: 'Dense',
    resultsFound: 'results found',
    category: 'Category',
  },
  common: {
    successResult: 'Completed sucessfully',
    credits: 'Credits',
    search: 'Search',
    loading: 'Loading',
    welcome: 'Welcome',
    back: 'Back',
    next: 'Next',
    finish: 'Finish',
    overView: 'OverView',
    today: 'Today',
    yesterday: 'Yesterday',
    ago: 'ago',
    minutes: 'minutes',
    hours: 'hours',
    days: 'days',
    weeks: 'weeks',
    months: 'months',
    years: 'years',
    noData: 'No data available',
    seeAll: 'See All',
    markAllAsRead: 'Mark all as read',
    notifications: 'Notifications',
    emptyNotifications: 'No notifications yet',
    emptyMessage: 'You have no notifications at the moment',
    agentThinking: 'Agent is thinking...',
  },
  dashboard: {
    overView: 'OverView',
    categories: 'Categories',
    teams: 'Teams',
    title: 'Dashboard',
  },
  user: {
    title: 'hello',
  },
  settings: {
    title: 'Settings',
    description:
      'Customize your experience with theme preferences, language options, and developer tools—tailor the platform to your workflow',
    developerMode: {
      title: 'Developer Mode',
      description:
        'Enable the developer mode to use advanced tools like connecting a custom database.',
    },
    language: {
      title: 'Language',
      description: 'Choose your preferred language for a personalized experience.',
      english: 'English',
      arabic: 'Arabic',
    },
    theme: {
      title: 'Theme',
      description: 'Switch between light, dark, or system mode for optimal viewing.',
      light: 'Light',
      dark: 'Dark',
      system: 'System',
    },
  },
  search: {
    placeholder: 'Please enter keywords',
    notFound: 'Not found',
    noResults: 'No results found for',
    tryChecking: 'Try checking for typos or using complete words.',
  },
  pages: {
    noDataLabel: 'No data available',
    teams: {
      title: 'Teams',
      newChat: 'New Chat',
      pageTitle: 'Dashboard: Teams',
      recentlyUsed: 'Recently Used Teams',
      useTemplate: 'Use Template',
      createTeam: 'Create Team',
      chatWith: 'Chat with',
      description: 'Create and manage your teams to collaborate effectively',
      emptyChat: 'Hello, got a new analysis to start a UX process based on it?',
      name: 'Team Name',
      searchPlaceholder: 'Search teams or agents...',
      searchResults: 'Search Results',
      searchResultsFor: 'We found results for "{{query}}"',
      agents: 'Agents',
      teamMember: 'Team Member',
      noSearchResults: 'No results found',
      tryDifferentSearch: 'Try different keywords or check your spelling',
      stepper: {
        teamInfo: {
          label: 'Team Info',
          description: 'Enter the basic information about the team',
        },
        configuration: {
          label: 'Configuration',
          description: 'Add tools configuration',
        },
        instructions: {
          label: 'Instructions',
          description: 'Add instructions',
        },
        frequency: {
          label: 'Frequency',
          description: 'Add frequency',
        },
        step: 'Step',
        of: 'of',
        flowControl: 'Flow Control',
        flowControlDescription: 'Choose how your flow should behave',
        startDate: 'Start Date',
        instructionsHelperText: 'Use {} to highlight important text and @ to mention team members',
      },
    },
    profile: {
      title: 'My Profile',
      pageTitle: 'Dashboard: Profile',
      breadcrumbs: {
        dashboard: 'Dashboard',
        profile: 'Profile',
        myProfile: 'My Profile',
      },
      personalInfo: 'Personal Information',
      firstName: 'First Name',
      lastName: 'Last Name',
      email: 'Email',
      phone: 'Phone',
      country: 'Country',
      state: 'State/Region',
      city: 'City',
      address: 'Address',
      zipCode: 'Zip/Code',
      about: 'About',
      company: 'Company',
      role: 'Role',
      skills: 'Skills',
      changePassword: 'Change Password',
      currentPassword: 'Current Password',
      newPassword: 'New Password',
      confirmPassword: 'Confirm New Password',
      members: 'Members',
    },
    knowledgeBase: {
      title: 'Knowledge Base',
      pageTitle: 'Dashboard: Knowledge Base',
      description: 'Here you can upload your own files to help the agents to complete tasks',
      developerMode: 'DEVELOPER MODE',
      resultsFound: 'results found',
      category: 'Category',
      connectResources: 'Connect Resources',
      databases: 'Databases',
      apis: 'APIs',
      cloudPlatforms: 'Cloud Platforms',
      tools: 'Tools',
    },
    auth: {
      signIn: {
        title: 'Sign in',
        pageTitle: 'Sign in | Jwt',
        welcomeBack: 'Hi, Welcome back',
        subtitle:
          'Access your personalized dashboard, track your workflows, and manage your tasks.',
        email: 'Email address',
        password: 'Password',
        rememberMe: 'Remember me',
        forgotPassword: 'Forgot password?',
        dontHaveAccount: "Don't have an account?",
        signUp: 'Sign up',
      },
      signUp: {
        title: 'Sign up',
        pageTitle: 'Sign up | Jwt',
        welcomeMessage: 'Join us and start automating your workflows!',
        subtitle:
          'Sign up today to gain access to our platform, manage automations, and unlock the power of seamless task automation.',
        firstName: 'First name',
        lastName: 'Last name',
        email: 'Email address',
        password: 'Password',
        termsOfService: 'I agree to the Terms of Service and Privacy Policy',
        alreadyHaveAccount: 'Already have an account?',
        signIn: 'Sign in',
      },
      forgotPassword: {
        title: 'Forgot Password',
        pageTitle: 'Reset Password | Jwt',
        description:
          'Please enter the email address associated with your account and we will email you a link to reset your password.',
        email: 'Email address',
        sendLink: 'Send Reset Link',
        backToSignIn: 'Back to Sign in',
      },
      newPassword: {
        title: 'New Password',
        pageTitle: 'New Password | Jwt',
        description: 'Please set your new password',
        password: 'New Password',
        confirmPassword: 'Confirm New Password',
        updatePassword: 'Update Password',
        backToSignIn: 'Back to Sign in',
      },
    },
    error: {
      notFound: {
        title: '404 Page Not Found',
        description: "Sorry, we couldn't find the page you're looking for",
        goToHome: 'Go to Home',
      },
    },
  },
  navigation: {
    profile: {
      title: 'Profile',
      myProfile: 'My Profile',
      knowledgeBase: 'Knowledge Base',
      settings: 'Settings',
    },
  },
  notifications: {
    all: 'All',
    unread: 'Unread',
    archived: 'Archived',
    questionAnswered: 'Question answered!',
    questionAnsweredDesc: 'Your question #{{id}} has been answered, check it now.',
    newTeamMember: 'New team member joined',
    newTeamMemberDesc: '{{name}} has joined your {{team}} project.',
    projectUpdate: 'Project update',
    projectUpdateDesc: 'Your project "{{project}}" has been updated with new assets.',
    newMessage: 'New message',
    newMessageDesc: 'You have a new message from {{name}}.',
    taskCompleted: 'Task completed',
    taskCompletedDesc: 'Your task "{{task}}" has been completed successfully.',
    securityAlert: 'Security alert',
    securityAlertDesc: 'We detected a new login to your account from a new device.',
    accountSecurity: 'Account security',
    accountSecurityDesc: 'We noticed a login from a new device. Please verify it was you.',
    subscriptionRenewal: 'Subscription renewal',
    subscriptionRenewalDesc: 'Your subscription will renew in 7 days. Review your plan now.',
    newFeature: 'New feature available',
    newFeatureDesc: 'Try our new AI-powered design suggestions feature.',
    creditsAdded: 'Credits added',
    creditsAddedDesc: 'Your account has been credited with {{amount}} additional credits.',
    meetingScheduled: 'Meeting scheduled',
    meetingScheduledDesc: 'Team meeting has been scheduled for tomorrow at {{time}}.',
  },
  layout: {
    header: {
      search: 'Search...',
      menuButton: 'Menu',
      needHelp: 'Need help?',
      purchase: 'Purchase',
    },
    sidebar: {
      dashboard: 'Dashboard',
      teams: 'Teams',
      profile: 'Profile',
      settings: 'Settings',
      knowledgeBase: 'Knowledge Base',
    },
  },
};
