import { useState, useCallback, useMemo, useEffect } from 'react';
import { useBoolean } from 'src/hooks/use-boolean';
import { useSearchParams } from 'src/routes/hooks';

// Team member type
export interface TeamMember {
  id: string;
  name: string;
  avatarUrl: string;
  role: string;
}

// Team data type
export interface TeamData {
  id: string;
  name: string;
  description: string;
  type: string;
  members: TeamMember[];
  createdAt: Date;
  userCount?: number; // Number of users using this team template
  category?: string; // Category of the team (Social Media, Research, Marketing, etc.)
}

// Form values type
export interface TeamFormValues {
  name: string;
  description: string;
  type: string;
  members: string[]; // Array of member IDs
}

export function useTeamsView(initialTeams: TeamData[]) {
  const searchParams = useSearchParams();
  const [teams, setTeams] = useState<TeamData[]>(initialTeams);
  const [selectedTeam, setSelectedTeam] = useState<TeamData | null>(null);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [selectedId, setSelectedId] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isSearching, setIsSearching] = useState<boolean>(false);

  // Check for search query in URL parameters
  useEffect(() => {
    const query = searchParams.get('q');
    if (query) {
      setSearchQuery(query);
      setIsSearching(true);
    } else {
      setSearchQuery('');
      setIsSearching(false);
    }
  }, [searchParams]);

  // Get recently used teams from localStorage or use default ones
  const getRecentlyUsedTeams = () => {
    try {
      const recentlyUsedIds = JSON.parse(localStorage.getItem('recentlyUsedTeams') || '[]');

      // If there are no recently used teams in localStorage, initialize with the first 3 teams
      if (recentlyUsedIds.length === 0 && teams.length > 0) {
        const defaultIds = teams.slice(0, 3).map((team) => team.id);
        localStorage.setItem('recentlyUsedTeams', JSON.stringify(defaultIds));
        return teams.slice(0, 3);
      }

      // Get the teams that match the IDs in recentlyUsedIds
      const recentTeams = recentlyUsedIds
        .map((id: string) => teams.find((team) => team.id === id))
        .filter((team: unknown): team is TeamData => team !== undefined)
        .slice(0, 3); // Limit to 3 teams

      // If we couldn't find any teams (maybe they were deleted), return the first 3 teams
      return recentTeams.length > 0 ? recentTeams : teams.slice(0, 3);
    } catch (error) {
      console.error('Error getting recently used teams:', error);
      // Return the first 3 teams as default
      return teams.slice(0, 3);
    }
  };

  const [recentlyUsedTeams, setRecentlyUsedTeams] = useState<TeamData[]>(getRecentlyUsedTeams());

  // Function to add a team to recently used
  const addToRecentlyUsed = useCallback(
    (teamId: string) => {
      try {
        // Get current recently used teams
        const recentlyUsedIds = JSON.parse(localStorage.getItem('recentlyUsedTeams') || '[]');

        // Remove the team if it already exists
        const filteredIds = recentlyUsedIds.filter((id: string) => id !== teamId);

        // Add the team to the beginning of the array
        const newRecentlyUsedIds = [teamId, ...filteredIds].slice(0, 3);

        // Save to localStorage
        localStorage.setItem('recentlyUsedTeams', JSON.stringify(newRecentlyUsedIds));

        // Update state
        const updatedRecentlyUsed = newRecentlyUsedIds
          .map((id: string) => teams.find((team) => team.id === id))
          .filter((team: unknown): team is TeamData => team !== undefined);

        setRecentlyUsedTeams(updatedRecentlyUsed);
      } catch (error) {
        console.error('Error updating recently used teams:', error);
      }
    },
    [teams]
  );

  // Search functionality is now handled by the URL parameters
  // The useEffect hook above handles updating the search state based on URL parameters

  // Get filtered teams based on selected category and search query
  const filteredTeams = useMemo(() => {
    let result = selectedCategory
      ? teams.filter((team) => team.category === selectedCategory)
      : teams;

    if (searchQuery) {
      // Normalize the search query to handle Arabic text properly
      const normalizedQuery = searchQuery.toLowerCase().trim();

      result = result.filter(
        (team) =>
          team.name.toLowerCase().includes(normalizedQuery) ||
          team.description.toLowerCase().includes(normalizedQuery)
      );
    }

    return result;
  }, [teams, selectedCategory, searchQuery]);

  // Get filtered team members based on search query
  const filteredMembers = useMemo(() => {
    if (!searchQuery) return [];

    // Collect all unique members from all teams
    const allMembers: TeamMember[] = [];
    const memberIds = new Set<string>();

    teams.forEach((team) => {
      team.members.forEach((member) => {
        if (!memberIds.has(member.id)) {
          memberIds.add(member.id);
          allMembers.push(member);
        }
      });
    });

    // Normalize the search query to handle Arabic text properly
    const normalizedQuery = searchQuery.toLowerCase().trim();

    // Filter members based on search query
    return allMembers.filter(
      (member) =>
        member.name.toLowerCase().includes(normalizedQuery) ||
        member.role.toLowerCase().includes(normalizedQuery)
    );
  }, [teams, searchQuery]);

  // Form dialog
  const formDialog = useBoolean();

  // Handle opening the form dialog for adding a new team
  const handleAddTeam = useCallback(() => {
    setSelectedTeam(null);
    formDialog.onTrue();
  }, [formDialog]);

  // Handle opening the form dialog for editing a team
  const handleEditTeam = useCallback(
    (id: string) => {
      const team = teams.find((t) => t.id === id);
      if (team) {
        setSelectedTeam(team);
        formDialog.onTrue();
      }
    },
    [teams, formDialog]
  );

  // Handle viewing a team
  const handleViewTeam = useCallback((id: string) => {
    // Implement view functionality here
    console.log(`Viewing team with ID: ${id}`);
  }, []);

  // Handle opening the confirm dialog for deleting a team
  const handleOpenConfirmDialog = useCallback((id: string) => {
    setSelectedId(id);
    setOpenConfirmDialog(true);
  }, []);

  // Handle closing the confirm dialog
  const handleCloseConfirmDialog = useCallback(() => {
    setOpenConfirmDialog(false);
  }, []);

  // Handle deleting a team
  const handleDeleteTeam = useCallback(() => {
    if (selectedId) {
      setTeams((prev) => prev.filter((team) => team.id !== selectedId));
      setOpenConfirmDialog(false);
      setSelectedId(null);
    }
  }, [selectedId]);

  // Handle form submission
  const handleFormSubmit = useCallback(
    (data: TeamFormValues) => {
      if (selectedTeam) {
        // Edit existing team
        setTeams((prev) =>
          prev.map((team) => {
            if (team.id === selectedTeam.id) {
              // Find members from the selected member IDs
              const updatedMembers = selectedTeam.members.filter((member) =>
                data.members.includes(member.id)
              );

              return {
                ...team,
                ...data,
                members: updatedMembers,
              };
            }
            return team;
          })
        );
      } else {
        // Add new team
        // In a real app, you would fetch the actual member data based on IDs
        // For now, we'll create dummy members
        const dummyMembers: TeamMember[] = data.members.map((id, index) => ({
          id,
          name: `Team Member ${index + 1}`,
          avatarUrl: `/assets/images/avatar/avatar_${(index % 5) + 1}.jpg`,
          role: 'Member',
        }));

        const newTeam: TeamData = {
          id: String(teams.length + 1),
          name: data.name,
          description: data.description,
          type: data.type,
          members: dummyMembers,
          createdAt: new Date(),
        };

        setTeams((prev) => [...prev, newTeam]);
      }
      formDialog.onFalse();
    },
    [teams, formDialog, selectedTeam]
  );

  // Handle selecting a category
  const handleSelectCategory = useCallback((category: string) => {
    setSelectedCategory((prev) => (prev === category ? null : category));
  }, []);

  return {
    // State
    teams: filteredTeams,
    selectedTeam,
    openConfirmDialog,
    selectedId,
    formDialog,
    selectedCategory,
    recentlyUsedTeams,
    searchQuery,
    isSearching,
    filteredMembers,

    // Handlers
    handleAddTeam,
    handleEditTeam,
    handleViewTeam,
    handleOpenConfirmDialog,
    handleCloseConfirmDialog,
    handleDeleteTeam,
    handleFormSubmit,
    handleSelectCategory,
    addToRecentlyUsed,
  };
}
