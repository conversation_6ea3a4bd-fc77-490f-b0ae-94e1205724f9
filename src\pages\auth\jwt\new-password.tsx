import { Helmet } from 'react-helmet-async';

import { CONFIG } from 'src/config-global';

import { JwtNewPasswordView } from 'src/sections/auth/jwt';

// ----------------------------------------------------------------------

const metadata = { title: `New Password | Jwt - ${CONFIG.site.name}` };

export default function Page() {
  return (
    <>
      <Helmet>
        <title> {metadata.title}</title>
      </Helmet>

      <JwtNewPasswordView />
    </>
  );
}