import { useCallback, useState } from 'react';
import type { Breakpoint } from '@mui/material/styles';
import type { NavSectionProps } from 'src/components/nav-section';
import { useTranslation } from 'react-i18next';

import Box from '@mui/material/Box';
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import Avatar from '@mui/material/Avatar';
import LinearProgress from '@mui/material/LinearProgress';
import IconButton from '@mui/material/IconButton';

import { varAlpha, hideScrollY } from 'src/theme/styles';

import { useAuthContext, useMockedUser } from 'src/auth/hooks';
import { signOut } from 'src/auth/context/jwt';
import { useRouter } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';
import { Logo } from 'src/components/logo';
import { Scrollbar } from 'src/components/scrollbar';
import { NavSectionMini, NavSectionVertical } from 'src/components/nav-section';
import { AppButton } from 'src/components/common';
import { Iconify } from 'src/components/iconify';
import ConfirmDialog from 'src/components/custom-dialog/confirm-dialog';

// import { NavUpgrade } from '../components/nav-upgrade';

// ----------------------------------------------------------------------

export type NavVerticalProps = NavSectionProps & {
  isNavMini: boolean;
  layoutQuery: Breakpoint;
  onToggleNav: () => void;
  slots?: {
    topArea?: React.ReactNode;
    bottomArea?: React.ReactNode;
  };
};

export function NavVertical({
  sx,
  data,
  slots,
  isNavMini,
  layoutQuery,
  onToggleNav,
  ...other
}: NavVerticalProps) {
  const theme = useTheme();
  const router = useRouter();
  const { t } = useTranslation();
  const { user } = useMockedUser();

  // State for logout confirmation dialog
  const [openLogoutDialog, setOpenLogoutDialog] = useState(false);
  // State for search query
  const [searchQuery, setSearchQuery] = useState('');
  // State for theme mode
  const [isDarkMode, setIsDarkMode] = useState(theme.palette.mode === 'dark');

  // Handle opening the logout confirmation dialog
  const handleOpenLogoutDialog = () => {
    setOpenLogoutDialog(true);
  };

  // Handle closing the logout confirmation dialog
  const handleCloseLogoutDialog = () => {
    setOpenLogoutDialog(false);
  };
  const { checkUserSession } = useAuthContext();

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  // Handle theme toggle
  const handleThemeToggle = () => {
    setIsDarkMode(!isDarkMode);
    // Here you would typically call a theme context method to change the theme
  };

  // Handle logout after confirmation
  const handleLogout = useCallback(async () => {
    try {
      await signOut();
      await checkUserSession?.();

      setOpenLogoutDialog(false);
      router.push(paths.auth.jwt.signIn);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error during logout:', error);
    }
  }, [checkUserSession, router, setOpenLogoutDialog]);
  const renderNavVertical = (
    <>
      {slots?.topArea ?? (
        <Box sx={{ px: 3, pt: 3, pb: 2 }}>
          {/* Header with Workforces title and collapse button */}
          <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
            <Stack direction="row" alignItems="center" spacing={1}>
              <Logo />
              <Typography variant="h5" sx={{ fontWeight: 700, color: 'text.primary' }}>
                Workforces
              </Typography>
            </Stack>
            <IconButton
              onClick={onToggleNav}
              size="small"
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  bgcolor: 'action.hover',
                },
              }}
            >
              <Iconify icon="ri:side-bar-fill" sx={{ width: 20, height: 20 }} />
            </IconButton>
          </Stack>

          {/* Search Input */}
          <TextField
            fullWidth
            size="small"
            placeholder="Search"
            value={searchQuery}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify
                    icon="eva:search-fill"
                    sx={{ color: 'text.disabled', width: 20, height: 20 }}
                  />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                bgcolor: 'background.paper',
                '& fieldset': {
                  borderColor: 'divider',
                },
                '&:hover fieldset': {
                  borderColor: 'primary.main',
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'primary.main',
                },
              },
            }}
          />
        </Box>
      )}

      <Scrollbar fillContent>
        <NavSectionVertical data={data} sx={{ px: 2, flex: '1 1 auto' }} {...other} />

        {/* User Profile Section */}
        <Box sx={{ p: 2.5, mt: 'auto' }}>
          <Box
            sx={{
              bgcolor: 'background.paper',
              borderRadius: 3,
              p: 2.5,
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
            }}
          >
            <Stack spacing={2.5}>
              {/* User Info with Arrow */}
              <Stack direction="row" alignItems="center" spacing={2}>
                <Avatar src={user?.photoURL} alt={user?.displayName} sx={{ width: 40, height: 40 }}>
                  {user?.displayName?.charAt(0)}
                </Avatar>
                <Box sx={{ flex: 1, minWidth: 0 }}>
                  <Typography
                    variant="subtitle2"
                    noWrap
                    sx={{ fontWeight: 600, color: 'text.primary' }}
                  >
                    {user?.displayName || 'James Broeng'}
                  </Typography>
                  <Typography variant="caption" color="text.secondary" noWrap>
                    {user?.email || '<EMAIL>'}
                  </Typography>
                </Box>
                <IconButton size="small" sx={{ color: 'text.secondary' }}>
                  <Iconify icon="eva:arrow-ios-forward-fill" sx={{ width: 16, height: 16 }} />
                </IconButton>
              </Stack>

              {/* Progress Section with Trophy Icon */}
              <Stack direction="row" alignItems="center" spacing={2}>
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    borderRadius: 1.5,
                    bgcolor: 'grey.100',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Iconify
                    icon="solar:cup-bold"
                    sx={{ width: 18, height: 18, color: 'text.secondary' }}
                  />
                </Box>
                <Box sx={{ flex: 1 }}>
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}
                  >
                    50% <span style={{ color: '#9CA3AF', fontWeight: 400 }}>Completed</span>
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={50}
                    sx={{
                      height: 6,
                      borderRadius: 3,
                      bgcolor: 'grey.200',
                      '& .MuiLinearProgress-bar': {
                        borderRadius: 3,
                        bgcolor: 'primary.main',
                      },
                    }}
                  />
                </Box>
              </Stack>
            </Stack>
          </Box>
        </Box>

        {/* Theme Toggle Buttons */}
        <Box
          sx={{
            bgcolor: '#F3F4F6',
            borderRadius: 3,
            p: 0.5,
            display: 'flex',
            gap: 0.5,
            width: '90%',
            marginLeft: 'auto',
            marginRight: 'auto',
            mb: '10px',
          }}
        >
          <Box
            sx={{
              flex: 1,
              bgcolor: !isDarkMode ? 'white' : 'transparent',
              borderRadius: 2.5,
              py: 1.5,
              px: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              transition: 'all 0.2s',
              boxShadow: !isDarkMode ? '0 1px 3px rgba(0, 0, 0, 0.1)' : 'none',
              '&:hover': {
                bgcolor: !isDarkMode ? 'white' : '#E5E7EB',
              },
            }}
            onClick={() => handleThemeToggle()}
          >
            <Iconify
              icon="solar:sun-bold"
              sx={{
                width: 18,
                height: 18,
                color: !isDarkMode ? 'text.primary' : 'text.secondary',
              }}
            />
          </Box>
          <Box
            sx={{
              flex: 1,
              bgcolor: isDarkMode ? 'white' : 'transparent',
              borderRadius: 2.5,
              py: 1.5,
              px: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              transition: 'all 0.2s',
              boxShadow: isDarkMode ? '0 1px 3px rgba(0, 0, 0, 0.1)' : 'none',
              '&:hover': {
                bgcolor: isDarkMode ? 'white' : '#E5E7EB',
              },
            }}
            onClick={() => handleThemeToggle()}
          >
            <Iconify
              icon="solar:moon-bold"
              sx={{
                width: 18,
                height: 18,
                color: isDarkMode ? 'text.primary' : 'text.secondary',
              }}
            />
          </Box>
        </Box>

        {slots?.bottomArea}
      </Scrollbar>
    </>
  );

  const renderNavMini = (
    <>
      {slots?.topArea ?? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 2.5 }}>
          {/* Collapse Icon */}
          <IconButton
            onClick={onToggleNav}
            size="small"
            sx={{
              color: 'text.secondary',
              '&:hover': {
                bgcolor: 'action.hover',
              },
            }}
          >
            <Iconify icon="ri:side-bar-fill" sx={{ width: 20, height: 20 }} />
          </IconButton>
        </Box>
      )}

      <NavSectionMini
        data={data}
        sx={{ pb: 2, px: 0.5, ...hideScrollY, flex: '1 1 auto', overflowY: 'auto' }}
        {...other}
      />

      {/* Mini User Profile Section */}
      <Box sx={{ p: 1.5, mt: 'auto' }}>
        <Stack spacing={2} alignItems="center">
          {/* User Avatar with Online Status */}
          <Box sx={{ position: 'relative' }}>
            <Avatar src={user?.photoURL} alt={user?.displayName} sx={{ width: 40, height: 40 }}>
              {user?.displayName?.charAt(0)}
            </Avatar>
            {/* Online Status Indicator */}
            <Box
              sx={{
                position: 'absolute',
                bottom: 0,
                right: 0,
                width: 12,
                height: 12,
                bgcolor: '#10B981', // Green color for online status
                borderRadius: '50%',
                border: '2px solid white',
              }}
            />
          </Box>

          {/* Theme Toggle Button */}
          <IconButton
            size="small"
            onClick={() => handleThemeToggle()}
            sx={{
              bgcolor: 'grey.100',
              color: 'text.secondary',
              '&:hover': {
                bgcolor: 'grey.200',
              },
            }}
          >
            <Iconify icon="solar:moon-bold" sx={{ width: 18, height: 18 }} />
          </IconButton>
        </Stack>
      </Box>

      {slots?.bottomArea}
    </>
  );

  return (
    <Box
      sx={{
        top: 0,
        left: 0,
        height: '100vh',
        display: 'flex',
        position: 'fixed',
        flexDirection: 'column',
        bgcolor: '#F8F9FA', // Light gray background to match Figma design
        zIndex: 'var(--layout-nav-zIndex)',
        width: isNavMini ? 'var(--layout-nav-mini-width)' : 'var(--layout-nav-vertical-width)',
        borderRight: `1px solid ${varAlpha(theme.vars.palette.grey['500Channel'], 0.12)}`,
        borderRadius: '0 16px 16px 0', // Rounded right corners to match Figma
        transition: theme.transitions.create(['width'], {
          easing: 'var(--layout-transition-easing)',
          duration: 'var(--layout-transition-duration)',
        }),
        // Hide on mobile, show on medium screens and up
        [theme.breakpoints.down('md')]: {
          display: 'none',
        },
        // Add subtle shadow for depth like in Figma
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
        ...sx,
      }}
    >
      {isNavMini ? renderNavMini : renderNavVertical}

      {/* Logout Confirmation Dialog */}
      <ConfirmDialog
        open={openLogoutDialog}
        onClose={handleCloseLogoutDialog}
        close={handleCloseLogoutDialog}
        title={
          <Typography variant="h3" textAlign="center">
            {t('components.dialogs.logout')}
          </Typography>
        }
        content={<Typography variant="body1">{t('components.dialogs.logoutConfirm')}</Typography>}
        icon={
          <Box sx={{ textAlign: 'center' }}>
            <Iconify
              icon="eva:alert-triangle-fill"
              sx={{ width: 64, height: 64, color: 'warning.main' }}
            />
          </Box>
        }
        action={
          <Stack direction="row" justifyContent="center" spacing={1} sx={{ width: '100%' }}>
            <AppButton
              sx={{ width: '45%' }}
              label={t('components.accountMenu.logout')}
              variant="contained"
              color="primary"
              onClick={handleLogout}
            />
            <AppButton
              sx={{ width: '45%' }}
              label={t('components.buttons.cancel')}
              variant="outlined"
              color="inherit"
              onClick={handleCloseLogoutDialog}
            />
          </Stack>
        }
      />
    </Box>
  );
}
