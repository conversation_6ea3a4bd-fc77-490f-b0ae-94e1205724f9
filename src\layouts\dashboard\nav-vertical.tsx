import type { Breakpoint } from '@mui/material/styles';
import type { NavSectionProps } from 'src/components/nav-section';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Avatar from '@mui/material/Avatar';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import IconButton from '@mui/material/IconButton';
import Divider from '@mui/material/Divider';
import { useTheme } from '@mui/material/styles';

import { varAlpha } from 'src/theme/styles';

import { WorkforcesLogo } from 'src/components/logo/workforces-logo';
import { Scrollbar } from 'src/components/scrollbar';
import { Iconify } from 'src/components/iconify';
import { RouterLink } from 'src/routes/components';

import { useMockedUser } from 'src/auth/hooks';
import { useTranslation } from 'react-i18next';
import { useSettingsContext } from 'src/components/settings';
import { usePathname } from 'src/routes/hooks';
import { useState } from 'react';

import { NavToggleButton } from '../components/nav-toggle-button';

// ----------------------------------------------------------------------

export type NavVerticalProps = NavSectionProps & {
  isNavMini: boolean;
  layoutQuery: Breakpoint;
  onToggleNav: () => void;
  slots?: {
    topArea?: React.ReactNode;
    bottomArea?: React.ReactNode;
  };
};

export function NavVertical({
  sx,
  data,
  slots,
  isNavMini,
  layoutQuery,
  onToggleNav,
}: NavVerticalProps) {
  const theme = useTheme();
  const { user } = useMockedUser();
  const { t } = useTranslation();
  const settings = useSettingsContext();
  const pathname = usePathname();
  const [searchQuery, setSearchQuery] = useState('');

  // Custom navigation data for the new sidebar
  const sidebarNavData = [
    {
      title: t('common.overView'),
      path: '/dashboard',
      icon: <Iconify icon="material-symbols:dashboard-outline" width={20} height={20} />,
    },
    {
      title: t('dashboard.agents'),
      path: '/dashboard/agents',
      icon: <Iconify icon="mdi:robot-outline" width={20} height={20} />,
    },
    {
      title: t('dashboard.teams'),
      path: '/dashboard/teams',
      icon: <Iconify icon="mdi:account-group-outline" width={20} height={20} />,
    },
    {
      title: t('pages.knowledgeBase.title'),
      path: '/dashboard/profile/knowledge-base',
      icon: <Iconify icon="mdi:book-open-outline" width={20} height={20} />,
    },
    {
      title: t('settings.title'),
      path: '/dashboard/profile/settings',
      icon: <Iconify icon="mdi:cog-outline" width={20} height={20} />,
    },
  ];

  const renderNavVertical = (
    <>
      {slots?.topArea ?? (
        <Stack spacing={2} sx={{ p: 2.5 }}>
          {/* Header with Workforces logo and toggle */}
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <WorkforcesLogo disableLink />
            <IconButton
              onClick={onToggleNav}
              sx={{
                width: 32,
                height: 32,
                bgcolor: theme.vars.palette.action.hover,
                '&:hover': {
                  bgcolor: theme.vars.palette.action.selected,
                },
              }}
            >
              <Iconify
                icon={isNavMini ? 'eva:menu-outline' : 'eva:close-outline'}
                width={16}
                height={16}
              />
            </IconButton>
          </Stack>

          {/* Search Field */}
          <TextField
            placeholder={t('common.search')}
            size="small"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="eva:search-fill" width={16} height={16} />
                </InputAdornment>
              ),
              sx: {
                bgcolor:
                  theme.vars.palette.mode === 'dark'
                    ? varAlpha(theme.vars.palette.grey['500Channel'], 0.12)
                    : theme.vars.palette.background.neutral,
                borderRadius: 1,
                '& .MuiOutlinedInput-notchedOutline': {
                  border: 'none',
                },
                '& .MuiInputBase-input': {
                  color: theme.vars.palette.text.primary,
                  '&::placeholder': {
                    color: theme.vars.palette.text.secondary,
                    opacity: 1,
                  },
                },
              },
            }}
            sx={{
              '& .MuiInputBase-root': {
                height: 36,
              },
            }}
          />
        </Stack>
      )}

      <Scrollbar fillContent>
        {/* Navigation Items */}
        <Stack spacing={0.5} sx={{ px: 2, py: 1 }}>
          {sidebarNavData
            .filter(
              (item) =>
                searchQuery === '' || item.title.toLowerCase().includes(searchQuery.toLowerCase())
            )
            .map((item) => {
              const isActive =
                pathname === item.path ||
                (item.path !== '/dashboard' && pathname.startsWith(item.path));

              return (
                <Box
                  key={item.title}
                  component={RouterLink}
                  href={item.path}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1.5,
                    px: 1.5,
                    py: 1,
                    borderRadius: 1,
                    textDecoration: 'none',
                    color: isActive
                      ? theme.vars.palette.common.white
                      : theme.vars.palette.text.secondary,
                    bgcolor: isActive ? theme.vars.palette.primary.main : 'transparent',
                    transition: theme.transitions.create(['background-color', 'color'], {
                      duration: theme.transitions.duration.shorter,
                    }),
                    '&:hover': {
                      bgcolor: isActive
                        ? theme.vars.palette.primary.main
                        : theme.vars.palette.action.hover,
                      color: isActive
                        ? theme.vars.palette.common.white
                        : theme.vars.palette.text.primary,
                    },
                  }}
                >
                  {item.icon}
                  <Typography variant="body2" sx={{ fontWeight: isActive ? 600 : 400 }}>
                    {item.title}
                  </Typography>
                </Box>
              );
            })}
        </Stack>

        {/* Spacer */}
        <Box sx={{ flex: 1 }} />

        {/* Bottom Section */}
        <Stack spacing={2} sx={{ p: 2 }}>
          {/* User Profile */}
          <Stack direction="row" alignItems="center" spacing={1.5}>
            <Avatar src={user?.photoURL} alt={user?.displayName} sx={{ width: 32, height: 32 }}>
              {user?.displayName?.charAt(0)}
            </Avatar>
            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Typography variant="subtitle2" noWrap>
                {user?.displayName || 'James Broang'}
              </Typography>
              <Typography variant="caption" color="text.secondary" noWrap>
                {user?.email || '<EMAIL>'}
              </Typography>
            </Box>
            <IconButton size="small">
              <Iconify icon="eva:more-horizontal-fill" width={16} height={16} />
            </IconButton>
          </Stack>

          {/* Upgrade Button */}
          <Button
            variant="outlined"
            fullWidth
            sx={{
              borderColor: theme.vars.palette.divider,
              color: theme.vars.palette.text.primary,
              '&:hover': {
                borderColor: theme.vars.palette.primary.main,
                bgcolor: varAlpha(theme.vars.palette.primary.mainChannel, 0.08),
              },
            }}
          >
            Upgrade to Pro
          </Button>

          {/* Credits */}
          <Stack direction="row" alignItems="center" spacing={1}>
            <Iconify
              icon="mdi:lightning-bolt"
              width={16}
              height={16}
              color={theme.vars.palette.warning.main}
            />
            <Typography variant="caption" color="text.secondary">
              1462 Available
            </Typography>
            <Typography variant="caption" color="text.disabled">
              Free Plan
            </Typography>
          </Stack>

          <Divider />

          {/* Theme Toggle */}
          <Stack direction="row" justifyContent="center" spacing={1}>
            <IconButton
              onClick={() => settings.onUpdateField('colorScheme', 'light')}
              sx={{
                width: 32,
                height: 32,
                bgcolor:
                  settings.colorScheme === 'light'
                    ? theme.vars.palette.action.selected
                    : 'transparent',
              }}
            >
              <Iconify icon="eva:sun-fill" width={16} height={16} />
            </IconButton>
            <IconButton
              onClick={() => settings.onUpdateField('colorScheme', 'dark')}
              sx={{
                width: 32,
                height: 32,
                bgcolor:
                  settings.colorScheme === 'dark'
                    ? theme.vars.palette.action.selected
                    : 'transparent',
              }}
            >
              <Iconify icon="eva:moon-fill" width={16} height={16} />
            </IconButton>
          </Stack>
        </Stack>
      </Scrollbar>
    </>
  );

  const renderNavMini = (
    <>
      {slots?.topArea ?? (
        <Stack spacing={2} sx={{ p: 1.5, alignItems: 'center' }}>
          {/* Mini Logo */}
          <Box
            sx={{
              width: 32,
              height: 32,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 1,
              bgcolor: theme.vars.palette.primary.main,
            }}
          >
            <Iconify icon="mdi:account-group" width={20} height={20} color="white" />
          </Box>
        </Stack>
      )}

      <Scrollbar fillContent>
        {/* Mini Navigation Items */}
        <Stack spacing={0.5} sx={{ px: 1, py: 1 }}>
          {sidebarNavData.map((item) => {
            const isActive =
              pathname === item.path ||
              (item.path !== '/dashboard' && pathname.startsWith(item.path));

            return (
              <Box
                key={item.title}
                component={RouterLink}
                href={item.path}
                title={item.title}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 40,
                  height: 40,
                  borderRadius: 1,
                  textDecoration: 'none',
                  color: isActive
                    ? theme.vars.palette.common.white
                    : theme.vars.palette.text.secondary,
                  bgcolor: isActive ? theme.vars.palette.primary.main : 'transparent',
                  transition: theme.transitions.create(['background-color', 'color'], {
                    duration: theme.transitions.duration.shorter,
                  }),
                  '&:hover': {
                    bgcolor: isActive
                      ? theme.vars.palette.primary.main
                      : theme.vars.palette.action.hover,
                    color: isActive
                      ? theme.vars.palette.common.white
                      : theme.vars.palette.text.primary,
                  },
                }}
              >
                {item.icon}
              </Box>
            );
          })}
        </Stack>

        {/* Spacer */}
        <Box sx={{ flex: 1 }} />

        {/* Mini Bottom Section */}
        <Stack spacing={1} sx={{ p: 1, alignItems: 'center' }}>
          {/* Mini User Avatar */}
          <Avatar src={user?.photoURL} alt={user?.displayName} sx={{ width: 32, height: 32 }}>
            {user?.displayName?.charAt(0)}
          </Avatar>

          {/* Mini Theme Toggle */}
          <Stack spacing={0.5} alignItems="center">
            <IconButton
              size="small"
              onClick={() =>
                settings.onUpdateField(
                  'colorScheme',
                  settings.colorScheme === 'light' ? 'dark' : 'light'
                )
              }
              sx={{
                width: 28,
                height: 28,
              }}
            >
              <Iconify
                icon={settings.colorScheme === 'light' ? 'eva:moon-fill' : 'eva:sun-fill'}
                width={14}
                height={14}
              />
            </IconButton>
          </Stack>
        </Stack>
      </Scrollbar>
    </>
  );

  return (
    <Box
      sx={{
        top: 0,
        left: 0,
        height: 1,
        display: 'none',
        position: 'fixed',
        flexDirection: 'column',
        bgcolor: 'var(--layout-nav-bg)',
        zIndex: 'var(--layout-nav-zIndex)',
        width: isNavMini ? 'var(--layout-nav-mini-width)' : 'var(--layout-nav-vertical-width)',
        borderRight: `1px solid var(--layout-nav-border-color, ${varAlpha(theme.vars.palette.grey['500Channel'], 0.12)})`,
        transition: theme.transitions.create(['width'], {
          easing: 'var(--layout-transition-easing)',
          duration: 'var(--layout-transition-duration)',
        }),
        [theme.breakpoints.up(layoutQuery)]: {
          display: 'flex',
        },
        ...sx,
      }}
    >
      <NavToggleButton
        isNavMini={isNavMini}
        onClick={onToggleNav}
        sx={{
          display: 'none',
          [theme.breakpoints.up(layoutQuery)]: {
            display: 'inline-flex',
          },
        }}
      />
      {isNavMini ? renderNavMini : renderNavVertical}
    </Box>
  );
}
