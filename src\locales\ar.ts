import { TranslationKeys } from './types';

export const ar: TranslationKeys = {
  dialogs: {
    confirmDeleteMessage: 'هل أنت متأكد؟',
    alert: 'تنبيه',
    info: 'معلومات',
    success: 'نجاح',
    warning: 'تحذير',
    error: 'خطأ',
  },
  buttons: {
    delete: 'حذف',
    cancel: 'إلغاء',
    enabled: 'مفعل',
    disabled: 'معطل',
    upload: 'رفع',
    clear: 'مسح',
    submit: 'إرسال',
    save: 'حفظ',
    signOut: 'تسجيل الخروج',
    connect: 'اتصال',
    viewAll: 'عرض الكل',
    createNew: 'إنشاء جديد',
    addNew: 'إضافة جديد',
    search: 'بحث',
    goBack: 'رجوع',
    continue: 'متابعة',
    close: 'إغلاق',
    edit: 'تعديل',
    remove: 'إزالة',
    update: 'تحديث',
    next: 'التالي',
    previous: 'السابق',
    finish: 'إنهاء',
  },
  tables: {
    rowsPerPageLabel: 'صفوف لكل صفحة',
    dense: 'مضغوط',
    resultsFound: 'نتائج وجدت',
    category: 'الفئة',
  },
  common: {
    successResult: 'تم الانتهاء بنجاح',
    credits: 'رصيد',
    search: 'بحث',
    loading: 'جاري التحميل',
    welcome: 'مرحبًا',
    back: 'رجوع',
    next: 'التالي',
    finish: 'إنهاء',
    overView: 'ملخص',
    today: 'اليوم',
    yesterday: 'أمس',
    ago: 'منذ',
    minutes: 'دقائق',
    hours: 'ساعات',
    days: 'أيام',
    weeks: 'أسابيع',
    months: 'شهور',
    years: 'سنوات',
    noData: 'لا توجد بيانات متاحة',
    seeAll: 'عرض الكل',
    markAllAsRead: 'تحديد الكل كمقروء',
    notifications: 'الإشعارات',
    emptyNotifications: 'لا توجد إشعارات حتى الآن',
    emptyMessage: 'ليس لديك إشعارات في الوقت الحالي',
    agentThinking: 'الوكيل يفكر...',
  },
  dashboard: {
    overView: 'ملخص',
    categories: 'فئات',
    teams: 'الفرق',
    title: 'لوحة التحكم',
  },
  user: {
    title: 'مرحبًا',
  },
  settings: {
    title: 'الإعدادات',
    description:
      'خصص تجربتك باستخدام تفضيلات المظهر وخيارات اللغة وأدوات المطور - صمم المنصة وفقًا لسير عملك',
    developerMode: {
      title: 'وضع المطور',
      description: 'تمكين وضع المطور لاستخدام أدوات متقدمة مثل الاتصال بقاعدة بيانات مخصصة.',
    },
    language: {
      title: 'اللغة',
      description: 'اختر لغتك المفضلة للحصول على تجربة مخصصة.',
      english: 'الإنجليزية',
      arabic: 'العربية',
    },
    theme: {
      title: 'المظهر',
      description: 'التبديل بين الوضع الفاتح أو الداكن أو وضع النظام للعرض الأمثل.',
      light: 'فاتح',
      dark: 'داكن',
      system: 'النظام',
    },
  },
  search: {
    placeholder: 'الرجاء إدخال كلمات البحث',
    notFound: 'غير موجود',
    noResults: 'لا توجد نتائج لـ',
    tryChecking: 'حاول التحقق من الأخطاء المطبعية أو استخدام كلمات كاملة.',
  },
  pages: {
    noDataLabel: 'لا توجد بيانات متاحة',
    teams: {
      title: 'الفرق',
      newChat: 'محادثة جديدة',
      pageTitle: 'لوحة التحكم: الفرق',
      recentlyUsed: 'الفرق المستخدمة مؤخرًا',
      useTemplate: 'استخدام قالب الفريق',
      createTeam: 'إنشاء فريق',
      chatWith: 'الدردشة مع',
      description: 'إنشاء وإدارة فرقك للتعاون بشكل فعال',
      emptyChat: 'مرحبًا، هل لديك تحليل جديد لبدء عملية تجربة المستخدم بناءً عليه؟',
      name: 'اسم الفريق',
      searchPlaceholder: 'البحث عن الفرق أو الوكلاء...',
      searchResults: 'نتائج البحث',
      searchResultsFor: 'وجدنا نتائج لـ "{{query}}"',
      agents: 'الوكلاء',
      teamMember: 'عضو الفريق',
      noSearchResults: 'لم يتم العثور على نتائج',
      tryDifferentSearch: 'جرب كلمات مختلفة أو تحقق من التهجئة',
      stepper: {
        teamInfo: {
          label: 'معلومات الفريق',
          description: 'أدخل المعلومات الأساسية عن الفريق',
        },
        configuration: {
          label: 'الإعدادات',
          description: 'إضافة إعدادات الأدوات',
        },
        instructions: {
          label: 'التعليمات',
          description: 'إضافة التعليمات',
        },
        frequency: {
          label: 'التكرار',
          description: 'إضافة التكرار',
        },
        step: 'الخطوة',
        of: 'من',
        flowControl: 'التحكم بالتدفق',
        flowControlDescription: 'اختر كيفية عمل التدفق الخاص بك',
        startDate: 'تاريخ البدء',
        instructionsHelperText: 'استخدم {} لتمييز النص المهم و @ للإشارة إلى أعضاء الفريق',
      },
    },
    profile: {
      title: 'ملفي الشخصي',
      pageTitle: 'لوحة التحكم: الملف الشخصي',
      breadcrumbs: {
        dashboard: 'لوحة التحكم',
        profile: 'الملف الشخصي',
        myProfile: 'ملفي الشخصي',
      },
      personalInfo: 'المعلومات الشخصية',
      firstName: 'الاسم الأول',
      lastName: 'اسم العائلة',
      email: 'البريد الإلكتروني',
      phone: 'الهاتف',
      country: 'الدولة',
      state: 'المنطقة/المحافظة',
      city: 'المدينة',
      address: 'العنوان',
      zipCode: 'الرمز البريدي',
      about: 'نبذة عني',
      company: 'الشركة',
      role: 'الدور',
      skills: 'المهارات',
      changePassword: 'تغيير كلمة المرور',
      currentPassword: 'كلمة المرور الحالية',
      newPassword: 'كلمة المرور الجديدة',
      confirmPassword: 'تأكيد كلمة المرور الجديدة',
      members: 'الأعضاء',
    },
    knowledgeBase: {
      title: 'قاعدة المعرفة',
      pageTitle: 'لوحة التحكم: قاعدة المعرفة',
      description: 'هنا يمكنك تحميل ملفاتك الخاصة لمساعدة الوكلاء على إكمال المهام',
      developerMode: 'وضع المطور',
      resultsFound: 'نتائج وجدت',
      category: 'الفئة',
      connectResources: 'ربط الموارد',
      databases: 'قواعد البيانات',
      apis: 'واجهات برمجة التطبيقات',
      cloudPlatforms: 'منصات السحابة',
      tools: 'الأدوات',
    },
    auth: {
      signIn: {
        title: 'تسجيل الدخول',
        pageTitle: 'تسجيل الدخول | Jwt',
        welcomeBack: 'مرحبًا بعودتك',
        subtitle: 'الوصول إلى لوحة التحكم الخاصة بك، وتتبع سير العمل، وإدارة المهام.',
        email: 'عنوان البريد الإلكتروني',
        password: 'كلمة المرور',
        rememberMe: 'تذكرني',
        forgotPassword: 'نسيت كلمة المرور؟',
        dontHaveAccount: 'ليس لديك حساب؟',
        signUp: 'إنشاء حساب',
      },
      signUp: {
        title: 'إنشاء حساب',
        pageTitle: 'إنشاء حساب | Jwt',
        welcomeMessage: 'انضم إلينا وابدأ في أتمتة سير العمل الخاص بك!',
        subtitle:
          'سجل اليوم للوصول إلى منصتنا، وإدارة عمليات الأتمتة، وإطلاق قوة أتمتة المهام السلسة.',
        firstName: 'الاسم الأول',
        lastName: 'اسم العائلة',
        email: 'عنوان البريد الإلكتروني',
        password: 'كلمة المرور',
        termsOfService: 'أوافق على شروط الخدمة وسياسة الخصوصية',
        alreadyHaveAccount: 'لديك حساب بالفعل؟',
        signIn: 'تسجيل الدخول',
      },
      forgotPassword: {
        title: 'نسيت كلمة المرور',
        pageTitle: 'إعادة تعيين كلمة المرور | Jwt',
        description:
          'الرجاء إدخال عنوان البريد الإلكتروني المرتبط بحسابك وسنرسل لك رابطًا لإعادة تعيين كلمة المرور.',
        email: 'عنوان البريد الإلكتروني',
        sendLink: 'إرسال رابط إعادة التعيين',
        backToSignIn: 'العودة إلى تسجيل الدخول',
      },
      newPassword: {
        title: 'كلمة مرور جديدة',
        pageTitle: 'كلمة مرور جديدة | Jwt',
        description: 'الرجاء تعيين كلمة المرور الجديدة الخاصة بك',
        password: 'كلمة المرور الجديدة',
        confirmPassword: 'تأكيد كلمة المرور الجديدة',
        updatePassword: 'تحديث كلمة المرور',
        backToSignIn: 'العودة إلى تسجيل الدخول',
      },
    },
    error: {
      notFound: {
        title: '404 الصفحة غير موجودة',
        description: 'عذرًا، لم نتمكن من العثور على الصفحة التي تبحث عنها',
        goToHome: 'الذهاب إلى الصفحة الرئيسية',
      },
    },
  },
  navigation: {
    profile: {
      title: 'الملف الشخصي',
      myProfile: 'ملفي الشخصي',
      knowledgeBase: 'قاعدة المعرفة',
      settings: 'الإعدادات',
    },
  },
  notifications: {
    all: 'الكل',
    unread: 'غير مقروءة',
    archived: 'مؤرشفة',
    questionAnswered: 'تم الإجابة على السؤال!',
    questionAnsweredDesc: 'تمت الإجابة على سؤالك رقم {{id}}، تحقق منه الآن.',
    newTeamMember: 'انضم عضو جديد للفريق',
    newTeamMemberDesc: 'انضم {{name}} إلى مشروع {{team}} الخاص بك.',
    projectUpdate: 'تحديث المشروع',
    projectUpdateDesc: 'تم تحديث مشروعك "{{project}}" بأصول جديدة.',
    newMessage: 'رسالة جديدة',
    newMessageDesc: 'لديك رسالة جديدة من {{name}}.',
    taskCompleted: 'تم إكمال المهمة',
    taskCompletedDesc: 'تم إكمال مهمتك "{{task}}" بنجاح.',
    securityAlert: 'تنبيه أمني',
    securityAlertDesc: 'اكتشفنا تسجيل دخول جديد إلى حسابك من جهاز جديد.',
    accountSecurity: 'أمان الحساب',
    accountSecurityDesc: 'لاحظنا تسجيل دخول من جهاز جديد. يرجى التحقق من أنه أنت.',
    subscriptionRenewal: 'تجديد الاشتراك',
    subscriptionRenewalDesc: 'سيتم تجديد اشتراكك خلال 7 أيام. راجع خطتك الآن.',
    newFeature: 'ميزة جديدة متاحة',
    newFeatureDesc: 'جرب ميزة اقتراحات التصميم المدعومة بالذكاء الاصطناعي الجديدة.',
    creditsAdded: 'تمت إضافة رصيد',
    creditsAddedDesc: 'تمت إضافة {{amount}} رصيد إضافي إلى حسابك.',
    meetingScheduled: 'تم جدولة اجتماع',
    meetingScheduledDesc: 'تم جدولة اجتماع الفريق ليوم غد الساعة {{time}}.',
  },
  layout: {
    header: {
      search: 'بحث...',
      menuButton: 'القائمة',
      needHelp: 'تحتاج مساعدة؟',
      purchase: 'شراء',
    },
    sidebar: {
      dashboard: 'لوحة التحكم',
      teams: 'الفرق',
      profile: 'الملف الشخصي',
      settings: 'الإعدادات',
      knowledgeBase: 'قاعدة المعرفة',
    },
  },
};
