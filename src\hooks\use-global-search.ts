import { useCallback } from 'react';
import { useRouter, usePathname } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';

/**
 * Custom hook to handle global search functionality
 * This hook provides a way to search across different pages using the main header's search bar
 */
export function useGlobalSearch() {
  const router = useRouter();
  const pathname = usePathname();

  /**
   * Handle global search
   * @param query The search query
   */
  const handleGlobalSearch = useCallback(
    (query: string) => {
      // Normalize the query by trimming whitespace
      const normalizedQuery = query.trim();

      // If we're on the teams page, update the URL with the search query
      if (pathname.includes(paths.dashboard.teams.root)) {
        if (normalizedQuery) {
          const url = `${paths.dashboard.teams.root}?q=${encodeURIComponent(normalizedQuery)}`;
          router.push(url);
        } else {
          // If query is empty, remove the query parameter
          router.push(paths.dashboard.teams.root);
        }
      } else if (normalizedQuery) {
        // If we're not on the teams page and there's a query, navigate to the teams page with the query
        const url = `${paths.dashboard.teams.root}?q=${encodeURIComponent(normalizedQuery)}`;
        router.push(url);
      }
    },
    [pathname, router]
  );

  return { handleGlobalSearch };
}
