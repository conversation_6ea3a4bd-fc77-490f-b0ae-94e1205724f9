import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Unstable_Grid2';

import { DashboardContent } from 'src/layouts/dashboard';

import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

export function AgentsView() {
  const { t } = useTranslation();

  const mockAgents = [
    {
      id: 1,
      name: 'Data Analyst Agent',
      description: 'Specialized in data analysis and visualization',
      icon: 'mdi:chart-line',
      status: 'active',
    },
    {
      id: 2,
      name: 'Content Writer Agent',
      description: 'Creates engaging content and copywriting',
      icon: 'mdi:pencil',
      status: 'active',
    },
    {
      id: 3,
      name: 'Research Agent',
      description: 'Conducts thorough research and analysis',
      icon: 'mdi:magnify',
      status: 'inactive',
    },
    {
      id: 4,
      name: 'Code Review Agent',
      description: 'Reviews code quality and suggests improvements',
      icon: 'mdi:code-tags',
      status: 'active',
    },
  ];

  return (
    <DashboardContent>
      <Container maxWidth="xl">
        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 5 }}>
          <Typography variant="h4">{t('dashboard.agents')}</Typography>
          <Button
            variant="contained"
            startIcon={<Iconify icon="eva:plus-fill" />}
          >
            Create Agent
          </Button>
        </Stack>

        <Grid container spacing={3}>
          {mockAgents.map((agent) => (
            <Grid key={agent.id} xs={12} sm={6} md={4} lg={3}>
              <Card
                sx={{
                  p: 3,
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  transition: (theme) => theme.transitions.create(['box-shadow'], {
                    duration: theme.transitions.duration.shorter,
                  }),
                  '&:hover': {
                    boxShadow: (theme) => theme.customShadows.z8,
                  },
                }}
              >
                <Stack spacing={2} sx={{ flex: 1 }}>
                  <Box
                    sx={{
                      width: 48,
                      height: 48,
                      borderRadius: 1.5,
                      bgcolor: 'primary.lighter',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Iconify 
                      icon={agent.icon} 
                      width={24} 
                      height={24} 
                      sx={{ color: 'primary.main' }}
                    />
                  </Box>

                  <Stack spacing={1}>
                    <Typography variant="h6" noWrap>
                      {agent.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {agent.description}
                    </Typography>
                  </Stack>

                  <Box
                    sx={{
                      px: 1,
                      py: 0.5,
                      borderRadius: 0.75,
                      bgcolor: agent.status === 'active' ? 'success.lighter' : 'grey.200',
                      width: 'fit-content',
                    }}
                  >
                    <Typography
                      variant="caption"
                      sx={{
                        color: agent.status === 'active' ? 'success.darker' : 'text.secondary',
                        fontWeight: 600,
                      }}
                    >
                      {agent.status === 'active' ? 'Active' : 'Inactive'}
                    </Typography>
                  </Box>
                </Stack>

                <Stack direction="row" spacing={1} sx={{ mt: 2 }}>
                  <Button size="small" variant="outlined" fullWidth>
                    Configure
                  </Button>
                  <Button size="small" variant="contained" fullWidth>
                    Use Agent
                  </Button>
                </Stack>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>
    </DashboardContent>
  );
}
