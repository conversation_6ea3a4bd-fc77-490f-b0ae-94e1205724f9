import { useState, useRef, useEffect } from 'react';
import {
  Box,
  Card,
  Grid,
  Avatar,
  Typography,
  LinearProgress,
  IconButton,
  styled,
  useTheme,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { useTranslation } from 'react-i18next';

// ----------------------------------------------------------------------

const SCROLL_SPEED = 400; // pixels to scroll per click

const CarouselArrowButton = styled(IconButton)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  transform: 'translateY(-50%)',
  zIndex: 9,
  width: 36,
  height: 36,
  borderRadius: '50%',
  backgroundColor: theme.palette.background.paper,
  boxShadow: theme.customShadows.z8,
  color: '#FF6B35',
  '&:hover': {
    backgroundColor: theme.palette.background.paper,
    color: '#E55A2A',
  },
  [theme.breakpoints.down('sm')]: {
    width: 30,
    height: 30,
  },
}));

// ----------------------------------------------------------------------

type CloudPlatform = {
  id: string;
  name: string;
  icon: string;
  files: number;
  color: string;
  progress: number;
};

type CloudPlatformsCarouselProps = {
  platforms: CloudPlatform[];
};

export default function CloudPlatformsCarousel({ platforms }: CloudPlatformsCarouselProps) {
  const theme = useTheme();
  const { i18n, t } = useTranslation();
  const isRtl = theme.direction === 'rtl';

  const scrollRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [maxScroll, setMaxScroll] = useState(0);

  // Reset scroll position when direction changes
  useEffect(() => {
    if (scrollRef.current) {
      // Reset to start position when direction changes
      scrollRef.current.scrollTo({
        left: 0,
        behavior: 'auto',
      });
      setScrollPosition(0);
    }
  }, [isRtl]);

  // Calculate maximum scroll width on mount and window resize
  useEffect(() => {
    const updateScrollInfo = () => {
      if (scrollRef.current) {
        const { scrollWidth, clientWidth } = scrollRef.current;
        const maxScrollValue = scrollWidth - clientWidth;
        setMaxScroll(maxScrollValue);

        // Update arrow visibility based on current position and direction
        if (isRtl) {
          // In RTL mode, scrollLeft is negative and starts from -maxScroll
          const absScrollPos = Math.abs(scrollPosition);
          setShowLeftArrow(absScrollPos < maxScrollValue - 10);
          setShowRightArrow(absScrollPos > 0);
        } else {
          // In LTR mode
          setShowLeftArrow(scrollPosition > 0);
          setShowRightArrow(scrollPosition < maxScrollValue - 10);
        }
      }
    };

    updateScrollInfo();
    window.addEventListener('resize', updateScrollInfo);

    return () => {
      window.removeEventListener('resize', updateScrollInfo);
    };
  }, [scrollPosition, isRtl]);

  const handleScrollLeft = () => {
    if (scrollRef.current) {
      // In RTL mode, "left" means moving towards the end of the content (more negative scrollLeft)
      // In LTR mode, "left" means moving towards the start of the content (less positive scrollLeft)
      const newPosition = isRtl
        ? Math.max(-maxScroll, scrollPosition - SCROLL_SPEED) // More negative in RTL
        : Math.max(0, scrollPosition - SCROLL_SPEED); // Less positive in LTR

      scrollRef.current.scrollTo({
        left: newPosition,
        behavior: 'smooth',
      });
      setScrollPosition(newPosition);
    }
  };

  const handleScrollRight = () => {
    if (scrollRef.current) {
      // In RTL mode, "right" means moving towards the start of the content (less negative scrollLeft)
      // In LTR mode, "right" means moving towards the end of the content (more positive scrollLeft)
      const newPosition = isRtl
        ? Math.min(0, scrollPosition + SCROLL_SPEED) // Less negative in RTL
        : Math.min(maxScroll, scrollPosition + SCROLL_SPEED); // More positive in LTR

      scrollRef.current.scrollTo({
        left: newPosition,
        behavior: 'smooth',
      });
      setScrollPosition(newPosition);
    }
  };

  const handleScroll = () => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      setScrollPosition(scrollLeft);

      const maxScrollValue = scrollWidth - clientWidth;

      if (isRtl) {
        // In RTL mode
        const absScrollPos = Math.abs(scrollLeft);
        setShowLeftArrow(absScrollPos < maxScrollValue - 10);
        setShowRightArrow(absScrollPos > 0);
      } else {
        // In LTR mode
        setShowLeftArrow(scrollLeft > 0);
        setShowRightArrow(scrollLeft < maxScrollValue - 10);
      }
    }
  };

  return (
    <Box sx={{ position: 'relative', mb: 4 }}>
      <CarouselArrowButton
        onClick={handleScrollLeft}
        sx={{
          left: { xs: -10, sm: -15, md: -18 },
          boxShadow: (theme) => theme.customShadows.z8,
          zIndex: 10,
          transform: isRtl ? 'rotate(180deg)' : '',
          opacity: showLeftArrow ? 1 : 0.5,
          pointerEvents: showLeftArrow ? 'auto' : 'none',
        }}
        aria-label="previous platforms"
        disabled={!showLeftArrow}
      >
        <Iconify icon="eva:arrow-ios-back-fill" width={20} />
      </CarouselArrowButton>

      <CarouselArrowButton
        onClick={handleScrollRight}
        sx={{
          right: { xs: -10, sm: -15, md: -18 },
          boxShadow: (theme) => theme.customShadows.z8,
          zIndex: 10,
          transform: isRtl ? 'rotate(180deg)' : '',
          opacity: showRightArrow ? 1 : 0.5,
          pointerEvents: showRightArrow ? 'auto' : 'none',
        }}
        aria-label="next platforms"
        disabled={!showRightArrow}
      >
        <Iconify icon="eva:arrow-ios-forward-fill" width={20} />
      </CarouselArrowButton>

      <Box
        ref={scrollRef}
        onScroll={handleScroll}
        sx={{
          display: 'flex',
          overflowX: 'auto',
          gap: 3,
          py: 1,
          px: { xs: 2, sm: 3, md: 4 },
          scrollbarWidth: 'none', // Firefox
          msOverflowStyle: 'none', // IE/Edge
          '&::-webkit-scrollbar': {
            // Chrome/Safari/Opera
            display: 'none',
          },
        }}
      >
        {platforms.map((platform) => (
          <Card
            key={platform.id}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              p: 2,
              boxShadow: 'none',
              border: '1px solid #f0f0f0',
              minWidth: { xs: 150, sm: 170, md: 190 },
              maxWidth: { xs: 150, sm: 170, md: 190 },
              height: 160,
              flexShrink: 0,
              cursor: 'pointer',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: (theme) => theme.customShadows.z8,
                borderColor: '#FF6B35',
              },
            }}
          >
            <Avatar
              src={platform.icon}
              sx={{ width: 40, height: 40, mb: 1, bgcolor: 'transparent' }}
            />
            <Typography variant="subtitle2" sx={{ mb: 0.5 }}>
              {platform.name}
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ mb: 1 }}>
              {platform.files} files
            </Typography>
            <Box sx={{ width: '100%', mt: 'auto' }}>
              <LinearProgress
                variant="determinate"
                value={platform.progress}
                sx={{
                  height: 4,
                  borderRadius: 2,
                  bgcolor: '#f5f5f5',
                  '& .MuiLinearProgress-bar': {
                    bgcolor: '#FF6B35',
                    borderRadius: 2,
                  },
                }}
              />
            </Box>
          </Card>
        ))}
      </Box>
    </Box>
  );
}
