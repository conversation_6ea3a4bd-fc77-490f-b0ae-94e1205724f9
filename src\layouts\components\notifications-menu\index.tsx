import type { IconButtonProps } from '@mui/material/IconButton';

import { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import Box from '@mui/material/Box';
import Menu from '@mui/material/Menu';
import Badge from '@mui/material/Badge';
import Stack from '@mui/material/Stack';
import Divider from '@mui/material/Divider';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';

import { Iconify } from 'src/components/iconify';
import {
  _notifications,
  getNotificationsByDate,
  NotificationItemProps,
} from 'src/_mock/_notifications';

import { NotificationItem } from './notification-item';

// ----------------------------------------------------------------------

export type NotificationsMenuProps = IconButtonProps & {
  data?: NotificationItemProps[];
  onMarkAllAsRead?: () => void;
  onViewAll?: () => void;
};

export function NotificationsMenu({
  data = _notifications,
  onMarkAllAsRead,
  onViewAll,
  sx,
  ...other
}: NotificationsMenuProps) {
  const { t } = useTranslation();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleOpenMenu = useCallback((event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  }, []);

  const handleCloseMenu = useCallback(() => {
    setAnchorEl(null);
  }, []);

  const [notifications, setNotifications] = useState(data);
  const [totalUnRead, setTotalUnRead] = useState(0);

  // Update notifications when data changes
  useEffect(() => {
    setNotifications(data);
  }, [data]);

  // Calculate total unread notifications
  useEffect(() => {
    setTotalUnRead(notifications.filter((item) => item.isUnRead === true).length);
  }, [notifications]);

  const handleMarkAllAsRead = useCallback(() => {
    setNotifications(notifications.map((notification) => ({ ...notification, isUnRead: false })));
    // Call the provided callback if available
    if (onMarkAllAsRead) {
      onMarkAllAsRead();
    } else {
      // Default behavior
      console.log('Marking all notifications as read');
    }
  }, [notifications, onMarkAllAsRead]);

  const handleViewAll = useCallback(() => {
    handleCloseMenu();
    // Call the provided callback if available
    if (onViewAll) {
      onViewAll();
    } else {
      // Default behavior
      console.log('Navigate to all notifications page');
    }
  }, [handleCloseMenu, onViewAll]);

  // Group notifications by date
  const {
    today: todayNotifications,
    yesterday: yesterdayNotifications,
    older: olderNotifications,
  } = getNotificationsByDate();

  return (
    <>
      <IconButton
        onClick={handleOpenMenu}
        sx={{
          position: 'relative',
          width: 40,
          height: 40,
          borderRadius: '50%',
          backgroundColor: 'white',
          border: '1px solid #E0E0E0',
          '&:hover': {
            backgroundColor: '#F5F5F5',
          },
          ...sx,
        }}
        {...other}
      >
        <Badge
        // badgeContent={totalUnRead > 0 ? 1 : 0}
        // variant="dot"
        // color="error"
        // sx={{
        //   '& .MuiBadge-badge': {
        //     backgroundColor: '#FF6B35',
        //     color: '#fff',
        //     minWidth: '8px',
        //     height: '8px',
        //     padding: 0,
        //     top: 8,
        //     right: 8,
        //   },
        // }}
        >
          <Iconify
            icon="mdi:bell-outline"
            width={22}
            height={22}
            sx={{
              color: '#757575',
            }}
          />
        </Badge>
      </IconButton>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleCloseMenu}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            sx: {
              width: 380,
              p: 0,
              mt: 1.5,
              ml: 0.75,
              borderRadius: 2,
              boxShadow: '0px 4px 15px rgba(0, 0, 0, 0.1)',
              border: '1px solid #E0E0E0',
              overflow: 'hidden',
            },
          },
        }}
      >
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          sx={{
            py: 2,
            px: 2.5,
            borderBottom: '1px solid #EEEEEE',
          }}
        >
          <Typography
            variant="h6"
            sx={{
              fontWeight: 600,
              fontSize: '1.1rem',
            }}
          >
            {t('common.notifications')}
          </Typography>

          <Stack direction="row" spacing={2}>
            <Typography
              variant="body2"
              onClick={handleMarkAllAsRead}
              sx={{
                fontSize: '0.875rem',
                color: '#FF6B35',
                cursor: 'pointer',
                '&:hover': {
                  textDecoration: 'underline',
                },
              }}
            >
              {t('common.markAllAsRead')}
            </Typography>
            <Typography
              variant="body2"
              onClick={handleViewAll}
              sx={{
                fontSize: '0.875rem',
                color: '#757575',
                cursor: 'pointer',
                '&:hover': {
                  textDecoration: 'underline',
                },
              }}
            >
              {t('common.seeAll')}
            </Typography>
          </Stack>
        </Stack>

        <Divider sx={{ borderStyle: 'solid' }} />

        <Box
          sx={{
            height: { xs: 340, sm: 400 },
            overflow: 'auto',
            scrollbarWidth: 'thin',
            '&::-webkit-scrollbar': {
              width: '4px',
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: '#f5f5f5',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: '#E0E0E0',
              borderRadius: '4px',
            },
            '&::-webkit-scrollbar-thumb:hover': {
              backgroundColor: '#BDBDBD',
            },
          }}
        >
          {notifications.length === 0 ? (
            <Box sx={{ py: 8, textAlign: 'center' }}>
              <Box
                sx={{
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  backgroundColor: '#F5F5F5',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mx: 'auto',
                  mb: 2,
                  border: '1px solid #EEEEEE',
                }}
              >
                <Iconify icon="mdi:bell-outline" width={32} height={32} sx={{ color: '#757575' }} />
              </Box>
              <Typography variant="subtitle1" gutterBottom>
                {t('common.emptyNotifications')}
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary', px: 4 }}>
                {t('common.emptyMessage')}
              </Typography>
            </Box>
          ) : (
            <>
              {todayNotifications.length > 0 && (
                <>
                  <Typography
                    variant="caption"
                    sx={{
                      p: 2,
                      display: 'block',
                      color: 'text.secondary',
                      borderBottom: '1px solid #EEEEEE',
                    }}
                  >
                    {t('common.today')}
                  </Typography>

                  {todayNotifications.map((notification) => (
                    <NotificationItem key={notification.id} notification={notification} />
                  ))}
                </>
              )}

              {yesterdayNotifications.length > 0 && (
                <>
                  <Typography
                    variant="caption"
                    sx={{
                      p: 2,
                      display: 'block',
                      color: 'text.secondary',
                      borderBottom: '1px solid #EEEEEE',
                    }}
                  >
                    {t('common.yesterday')}
                  </Typography>

                  {yesterdayNotifications.map((notification) => (
                    <NotificationItem key={notification.id} notification={notification} />
                  ))}
                </>
              )}

              {olderNotifications.length > 0 && (
                <>
                  <Typography
                    variant="caption"
                    sx={{
                      p: 2,
                      display: 'block',
                      color: 'text.secondary',
                      borderBottom: '1px solid #EEEEEE',
                    }}
                  >
                    Earlier
                  </Typography>

                  {olderNotifications.map((notification) => (
                    <NotificationItem key={notification.id} notification={notification} />
                  ))}
                </>
              )}
            </>
          )}
        </Box>
      </Menu>
    </>
  );
}
