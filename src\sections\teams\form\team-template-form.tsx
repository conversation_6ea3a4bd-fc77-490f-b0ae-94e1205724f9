import { useState, useRef, useMemo } from 'react';
import {
  Stack,
  Typography,
  Stepper,
  Step,
  StepConnector,
  Box,
  Grid,
  Card,
  CardContent,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
  Avatar,
  styled,
  useMediaQuery,
  IconButton,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { useDeveloperMode } from 'src/contexts/developer-mode-context';
import { Iconify } from 'src/components/iconify';
import { Field } from 'src/components/hook-form/fields';
import { Form } from 'src/components/hook-form/form-provider';
import { AppButton } from 'src/components/common';
import {
  useTeamTemplateForm,
  TeamTemplateFormValues,
  Team,
  FREQUENCY_OPTIONS,
} from './use-team-template-form';

// ----------------------------------------------------------------------

// Component props
interface TeamTemplateFormProps {
  team: Team | null;
  onSubmit: (_values: TeamTemplateFormValues) => void;
  onCancel: () => void;
}

export default function TeamTemplateForm({ team, onSubmit, onCancel }: TeamTemplateFormProps) {
  const { t } = useTranslation();
  const theme = useTheme();
  const { developerMode } = useDeveloperMode();
  const isMobile = useMediaQuery(theme.breakpoints.down(600)); // Custom breakpoint for mobile devices
  const isTablet = useMediaQuery(theme.breakpoints.between(600, 900)); // Custom breakpoint for tablet devices

  // Use the custom hook for form logic
  const {
    activeStep,
    methods,
    isSubmitting,
    handleNext,
    handleBack,
    resetForm,
    FLOW_CONTROL_OPTIONS,
    TEAM_TEMPLATE_FORM_STEPS,
    tools,
    isLoadingTools,
  } = useTeamTemplateForm({ team, onSubmit });

  // Use refs to maintain accordion state across re-renders
  const membersExpandedRef = useRef(false);
  const toolsExpandedRef = useRef(false);

  // Card component for overview and tools
  const OverviewCard = () => {
    // Use state that's initialized from the refs
    const [membersExpanded, setMembersExpanded] = useState(membersExpandedRef.current);
    const [toolsExpanded, setToolsExpanded] = useState(toolsExpandedRef.current);

    return (
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Stack spacing={3}>
            {/* Overview Section */}
            <Box>
              <Typography variant="h6" gutterBottom>
                {t('common.overView')}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                This team template provides a structured framework for collaboration and
                communication. It includes tools integration and workflow management.
              </Typography>
            </Box>

            {/* Members Section */}
            <Box>
              <Accordion
                expanded={membersExpanded}
                onChange={(_, expanded) => {
                  // Directly update both state and ref
                  setMembersExpanded(expanded);
                  membersExpandedRef.current = expanded;
                }}
                sx={{
                  width: '100%',
                  bgcolor: 'background.default',
                  borderRadius: 1,
                  mb: 2,
                  '& .MuiAccordionSummary-root': {
                    bgcolor: 'background.paper',
                    borderRadius: '8px 8px 0 0',
                  },
                  '& .MuiAccordionDetails-root': {
                    bgcolor: 'background.paper',
                    borderRadius: '0 0 8px 8px',
                  },
                }}
              >
                <AccordionSummary expandIcon={<Iconify icon="eva:arrow-ios-downward-fill" />}>
                  <Typography variant="subtitle2">{t('pages.profile.members')}</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Stack spacing={2}>
                    {[
                      {
                        id: 1,
                        name: 'Naofumi, the UX team lead',
                        avatar: '/assets/images/avatars/avatar_1.jpg',
                        description:
                          'Starts a comprehensive UX research based on a detailed analysis',
                      },
                      {
                        id: 2,
                        name: 'Naofumi, the UX team lead',
                        avatar: '/assets/images/avatars/avatar_2.jpg',
                        description:
                          'Starts a comprehensive UX research based on a detailed analysis',
                      },
                      {
                        id: 3,
                        name: 'Naofumi, the UX team lead',
                        avatar: '/assets/images/avatars/avatar_3.jpg',
                        description:
                          'Starts a comprehensive UX research based on a detailed analysis',
                      },
                    ].map((member) => (
                      <Box
                        key={member.id}
                        sx={{
                          p: 2,
                          bgcolor: 'background.neutral',
                          borderRadius: 1,
                        }}
                      >
                        <Stack direction="row" spacing={2}>
                          <Avatar src={member.avatar} alt={member.name} />
                          <Box>
                            <Typography variant="subtitle2">{member.name}</Typography>
                            <Typography variant="body2" color="text.secondary">
                              {member.description}
                            </Typography>
                          </Box>
                        </Stack>
                      </Box>
                    ))}
                  </Stack>
                </AccordionDetails>
              </Accordion>
            </Box>

            {/* Tools Section */}
            <Box>
              <Accordion
                expanded={toolsExpanded}
                onChange={(_, expanded) => {
                  // Directly update both state and ref
                  setToolsExpanded(expanded);
                  toolsExpandedRef.current = expanded;
                }}
                sx={{
                  width: '100%',
                  bgcolor: 'background.default',
                  borderRadius: 1,
                  '& .MuiAccordionSummary-root': {
                    bgcolor: 'background.paper',
                    borderRadius: '8px 8px 0 0',
                  },
                  '& .MuiAccordionDetails-root': {
                    bgcolor: 'background.paper',
                    borderRadius: '0 0 8px 8px',
                  },
                }}
              >
                <AccordionSummary expandIcon={<Iconify icon="eva:arrow-ios-downward-fill" />}>
                  <Typography variant="subtitle2">{t('pages.knowledgeBase.tools')}</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Stack spacing={2}>
                    {['Gmail', 'Facebook', 'LinkedIn'].map((tool) => (
                      <Card
                        key={tool}
                        variant="outlined"
                        sx={{ bgcolor: 'background.neutral', p: 2 }}
                      >
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Iconify
                            icon={
                              tool === 'Gmail'
                                ? 'logos:google-gmail'
                                : tool === 'Facebook'
                                  ? 'logos:facebook'
                                  : 'logos:linkedin-icon'
                            }
                            width={24}
                            height={24}
                          />
                          <Typography variant="subtitle2">{tool}</Typography>
                        </Stack>
                      </Card>
                    ))}
                  </Stack>
                </AccordionDetails>
              </Accordion>
            </Box>
          </Stack>
        </CardContent>
      </Card>
    );
  };

  // Memoize the OverviewCard to prevent re-rendering when flow control changes
  const memoizedOverviewCard = useMemo(() => <OverviewCard key="overview-card" />, []);

  // Define the step content - Use the translated labels from TEAM_TEMPLATE_FORM_STEPS
  const steps = [
    {
      label: TEAM_TEMPLATE_FORM_STEPS[0].label,
      icon: TEAM_TEMPLATE_FORM_STEPS[0].icon,
      description: TEAM_TEMPLATE_FORM_STEPS[0].description,
      fields: (
        <Stack spacing={2}>
          <Typography variant="h6">{t('pages.teams.createTeam')}</Typography>

          <Field.Text name="name" label={t('pages.teams.name')} />
          <Field.Text name="description" label={t('pages.teams.description')} multiline rows={3} />

          <Stack direction="row" justifyContent="space-between" spacing={1}>
            <Box>
              <Typography variant="h5">{t('pages.teams.stepper.flowControl')}</Typography>
              <Typography>{t('pages.teams.stepper.flowControlDescription')}</Typography>
            </Box>
            <Stack direction="row" spacing={1}>
              {FLOW_CONTROL_OPTIONS.map((option) => {
                const isSelected = methods.watch('flowControl') === option.value;
                return (
                  <AppButton
                    key={option.value}
                    onClick={() =>
                      methods.setValue('flowControl', option.value as 'auto' | 'manual')
                    }
                    sx={{ color: isSelected ? 'primary.main' : 'text.primary' }}
                    variant={isSelected ? 'soft' : 'text'}
                    label={option?.label}
                  />
                );
              })}
            </Stack>
          </Stack>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{ flexGrow: 1 }}>
              <Field.Select
                name="method"
                label="method"
                options={[
                  { value: 'roundRobin', label: 'Round Robin' },
                  { value: 'random', label: 'Random' },
                  { value: 'sequential', label: 'Sequential' },
                ]}
                defaultValue="roundRobin"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    bgcolor: (theme) =>
                      theme.palette.mode === 'dark' ? 'background.paper' : '#FFFFFF',
                    borderRadius: 1,
                  },
                  '& .MuiSelect-select': {
                    py: 1.5,
                  },
                }}
              />
            </Box>
          </Box>
        </Stack>
      ),
    },
    {
      label: TEAM_TEMPLATE_FORM_STEPS[1].label,
      icon: TEAM_TEMPLATE_FORM_STEPS[1].icon,
      description: TEAM_TEMPLATE_FORM_STEPS[1].description,
      fields: (
        <Stack spacing={1}>
          <Typography variant="h6">{TEAM_TEMPLATE_FORM_STEPS[1].label}</Typography>

          <Typography variant="body2" color="text.secondary">
            {TEAM_TEMPLATE_FORM_STEPS[1].description}
          </Typography>

          {/* Dynamic tools accordions */}
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2" sx={{ mb: 2 }}>
              Tool Configuration
            </Typography>

            {isLoadingTools ? (
              // Loading state
              <Box sx={{ p: 4, textAlign: 'center' }}>
                <CircularProgress size={40} />
                <Typography variant="body2" sx={{ mt: 2, color: 'text.secondary' }}>
                  Loading tools configuration...
                </Typography>
              </Box>
            ) : tools.length === 0 ? (
              // No tools available
              <Box
                sx={{
                  p: 4,
                  textAlign: 'center',
                  border: '1px dashed',
                  borderColor: 'divider',
                  borderRadius: 1,
                }}
              >
                <Typography variant="body2" color="text.secondary">
                  No tools available for configuration.
                </Typography>
              </Box>
            ) : (
              // Map through tools from API
              <>
                {tools.map((tool) => {
                  // Check if all required fields are filled
                  const requiredFields = tool.fields.filter((field) => field.required);
                  const allRequiredFieldsFilled = requiredFields.every((field) => {
                    const value = methods.watch(field.name as any);
                    return value !== undefined && value !== '';
                  });

                  return (
                    <Accordion
                      key={tool.id}
                      sx={{
                        mt: '10px',
                        width: '100%',
                        bgcolor: 'background.neutral',
                        borderRadius: 1,
                        '& .MuiAccordionSummary-root': {
                          bgcolor: 'background.neutral',
                          borderRadius: '8px 8px 0 0',
                        },
                        '& .MuiAccordionDetails-root': {
                          bgcolor: 'background.neutral',
                          borderRadius: '0 0 8px 8px',
                        },
                      }}
                    >
                      <AccordionSummary expandIcon={<Iconify icon="eva:arrow-ios-downward-fill" />}>
                        <Stack
                          direction="row"
                          alignItems="center"
                          spacing={2}
                          width="100%"
                          justifyContent="space-between"
                        >
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <Iconify icon={tool.icon} width={24} height={24} />
                            <Typography>{tool.name}</Typography>
                            <Chip
                              label="Done"
                              color="success"
                              size="small"
                              variant="soft"
                              sx={{
                                display: allRequiredFieldsFilled ? 'flex' : 'none',
                              }}
                            />
                          </Stack>
                        </Stack>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Stack spacing={2}>
                          {tool.fields.map((field) => {
                            // Function to handle save for this specific field
                            const handleSave = () => {
                              const value = methods.watch(field.name as any);
                              console.log(`Saving ${field.name} with value:`, value);
                              // Here you would typically call an API to save this field
                              // For now, we'll just show a success message
                              alert(`Saved ${field.label} successfully!`);
                            };

                            if (field.type === 'text') {
                              return (
                                <Box
                                  key={field.id}
                                  sx={{
                                    display: 'flex',
                                    alignItems: 'flex-start',
                                    justifyContent: 'space-between',
                                    gap: 1,
                                  }}
                                >
                                  <Field.Text
                                    name={field.name}
                                    label={field.label}
                                    required={field.required}
                                    sx={{
                                      backgroundColor: (theme) =>
                                        theme.palette.mode === 'dark'
                                          ? 'background.paper'
                                          : 'white',
                                      flexGrow: 1,
                                    }}
                                  />
                                  <AppButton
                                    label="Save"
                                    fullWidth={false}
                                    variant="soft"
                                    color="primary"
                                    size="small"
                                    onClick={handleSave}
                                    sx={{ mt: 1 }}
                                  />
                                </Box>
                              );
                            }

                            if (field.type === 'select' && field.options) {
                              return (
                                <Box
                                  key={field.id}
                                  sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}
                                >
                                  <Field.Select
                                    name={field.name}
                                    label={field.label}
                                    options={field.options}
                                    required={field.required}
                                    sx={{
                                      backgroundColor: (theme) =>
                                        theme.palette.mode === 'dark'
                                          ? 'background.paper'
                                          : 'white',
                                      flexGrow: 1,
                                    }}
                                  />
                                  <AppButton
                                    label="Save"
                                    variant="contained"
                                    color="primary"
                                    size="small"
                                    onClick={handleSave}
                                    sx={{ mt: 1 }}
                                  />
                                </Box>
                              );
                            }
                            return null;
                          })}
                        </Stack>
                      </AccordionDetails>
                    </Accordion>
                  );
                })}
              </>
            )}
          </Box>
        </Stack>
      ),
    },
    {
      label: TEAM_TEMPLATE_FORM_STEPS[2].label,
      icon: TEAM_TEMPLATE_FORM_STEPS[2].icon,
      description: TEAM_TEMPLATE_FORM_STEPS[2].description,
      fields: (
        <Stack spacing={1}>
          <Typography variant="h6">{TEAM_TEMPLATE_FORM_STEPS[2].label}</Typography>

          <Typography variant="body2" color="text.secondary">
            {TEAM_TEMPLATE_FORM_STEPS[2].description}
          </Typography>

          <Field.Instruction
            name="instructions"
            label={t('pages.teams.stepper.instructions.label')}
            rows={6}
            helperText={t('pages.teams.stepper.instructionsHelperText')}
          />
        </Stack>
      ),
    },
    {
      label: TEAM_TEMPLATE_FORM_STEPS[3].label,
      icon: TEAM_TEMPLATE_FORM_STEPS[3].icon,
      description: TEAM_TEMPLATE_FORM_STEPS[3].description,
      fields: (
        <Stack spacing={1}>
          <Typography variant="h6">{TEAM_TEMPLATE_FORM_STEPS[3].label}</Typography>

          <Typography variant="body2" color="text.secondary">
            {TEAM_TEMPLATE_FORM_STEPS[3].description}
          </Typography>
          <Grid container sx={{ mt: '14px' }} spacing={2}>
            <Grid item xs={12} md={6}>
              <Field.Select
                name="frequency"
                label={t('pages.teams.stepper.frequency.label')}
                options={FREQUENCY_OPTIONS}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Field.DatePicker name="startDate" label={t('pages.teams.stepper.startDate')} />
            </Grid>
          </Grid>
        </Stack>
      ),
    },
  ];

  // Create a custom connector for the stepper with responsive styling
  const CustomConnector = styled(StepConnector)(({ theme }) => ({
    [`& .MuiStepConnector-line`]: {
      height: 2,
      border: 0,
      backgroundColor:
        theme.palette.mode === 'dark'
          ? theme.palette.grey[700] // Darker color for dark mode
          : theme.palette.divider,
      borderRadius: 1,
      transition: 'background-color 0.3s ease',
      // Make the line shorter in client mode
      width: !developerMode ? '80px' : '100%',
    },
    // [`&.Mui-active .MuiStepConnector-line`]: {
    //   backgroundColor:
    //     theme.palette.mode === 'dark'
    //       ? 'rgba(255, 111, 60, 0.5)' // Semi-transparent orange for dark mode
    //       : 'rgba(255, 111, 60, 0.3)', // Orange color for active step
    // },
    // [`&.Mui-completed .MuiStepConnector-line`]: {
    //   backgroundColor:
    //     theme.palette.mode === 'dark'
    //       ? 'rgba(34, 197, 94, 0.5)' // Semi-transparent green for dark mode
    //       : 'rgba(34, 197, 94, 0.3)', // Green color for completed steps
    // },
    // Responsive adjustments for tablet and mobile
    [theme.breakpoints.down('md')]: {
      [`& .MuiStepConnector-line`]: {
        width: !developerMode ? '60px' : '100%',
      },
    },
    [theme.breakpoints.down('sm')]: {
      [`& .MuiStepConnector-line`]: {
        height: 2,
        width: !developerMode ? '40px' : '100%',
      },
    },
  }));

  return (
    <Form
      methods={methods}
      onSubmit={(_event?: React.FormEvent<HTMLFormElement>) => {
        // Always prevent default form submission
        // We'll handle submission manually with the Create Team button
        if (_event) _event.preventDefault();
      }}
    >
      {/* Stepper at the top - Different views for mobile and desktop */}
      <Box
        sx={{
          width: '100%',
          mb: 4,
          overflow: 'hidden', // Prevent horizontal scrolling
        }}
      >
        {isMobile ? (
          // Mobile-optimized stepper view
          <Box sx={{ width: '100%' }}>
            {/* Mobile progress indicator */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                mb: 2,
                px: 1,
              }}
            >
              {/* Left navigation button */}
              <IconButton
                onClick={handleBack}
                disabled={activeStep === 0}
                sx={{
                  visibility: activeStep === 0 ? 'hidden' : 'visible',
                  color: 'text.secondary',
                }}
              >
                <Iconify icon="eva:arrow-back-fill" width={20} height={20} />
              </IconButton>

              {/* Current step indicator */}
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 0.5 }}>
                  {TEAM_TEMPLATE_FORM_STEPS[activeStep].label}
                </Typography>
                <Typography
                  variant="caption"
                  color={
                    theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.7)' : 'text.secondary'
                  }
                >
                  {t('pages.teams.stepper.step')} {activeStep + 1} {t('pages.teams.stepper.of')}{' '}
                  {TEAM_TEMPLATE_FORM_STEPS.length}
                </Typography>
              </Box>

              {/* Right navigation button */}
              <IconButton
                onClick={handleNext}
                disabled={activeStep === TEAM_TEMPLATE_FORM_STEPS.length - 1}
                sx={{
                  visibility:
                    activeStep === TEAM_TEMPLATE_FORM_STEPS.length - 1 ? 'hidden' : 'visible',
                  color: 'text.secondary',
                }}
              >
                <Iconify icon="eva:arrow-forward-fill" width={20} height={20} />
              </IconButton>
            </Box>

            {/* Progress bar */}
            <Box sx={{ width: '100%', px: 2 }}>
              <Box
                sx={{
                  width: '100%',
                  height: 4,
                  bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'divider',
                  borderRadius: 2,
                  position: 'relative',
                  overflow: 'hidden',
                }}
              >
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    height: '100%',
                    width: `${(activeStep / (TEAM_TEMPLATE_FORM_STEPS.length - 1)) * 100}%`,
                    bgcolor:
                      theme.palette.mode === 'dark' ? 'rgba(255, 111, 60, 0.8)' : 'success.main',
                    borderRadius: 2,
                    transition: 'width 0.3s ease',
                    boxShadow:
                      theme.palette.mode === 'dark' ? '0 0 8px rgba(255, 111, 60, 0.5)' : 'none',
                  }}
                />
              </Box>

              {/* Step dots */}
              <Stack direction="row" justifyContent="space-between" sx={{ mt: 1, px: 1 }}>
                {TEAM_TEMPLATE_FORM_STEPS.map((_, index) => {
                  const isCompleted = index < activeStep;
                  const isActive = index === activeStep;
                  return (
                    <Box
                      key={index}
                      sx={{
                        width: 8,
                        height: 8,
                        borderRadius: '50%',
                        bgcolor:
                          isCompleted || isActive
                            ? theme.palette.mode === 'dark'
                              ? 'rgba(255, 111, 60, 0.8)'
                              : 'success.main'
                            : theme.palette.mode === 'dark'
                              ? 'rgba(255, 255, 255, 0.15)'
                              : 'grey.300',
                        transition: 'all 0.2s ease',
                        boxShadow:
                          (isCompleted || isActive) && theme.palette.mode === 'dark'
                            ? '0 0 4px rgba(255, 111, 60, 0.5)'
                            : 'none',
                      }}
                    />
                  );
                })}
              </Stack>
            </Box>
          </Box>
        ) : (
          // Desktop/Tablet stepper view
          <Stepper
            activeStep={activeStep}
            connector={<CustomConnector />}
            sx={{
              justifyContent: 'center',
              flexWrap: isTablet ? 'wrap' : 'nowrap',
              gap: isTablet ? 1 : 0,
              // Center the stepper and make it narrower in client mode
              maxWidth: !developerMode ? '400px' : '100%',
              margin: '0 auto',
              // Adjust spacing for different screen sizes
              '& .MuiStep-root': {
                px: { xs: 1, sm: 1.5, md: 2 },
                py: isTablet ? 1 : 0,
                flex: isTablet ? '1 1 45%' : '0 0 auto', // Two steps per row on tablet
                minWidth: isTablet ? '45%' : 'auto',
              },
            }}
          >
            {TEAM_TEMPLATE_FORM_STEPS.map((step, index) => {
              const isCompleted = index < activeStep;
              const isActive = index === activeStep;
              return (
                <Step key={step.label} completed={isCompleted}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      flexDirection: isTablet ? 'column' : { xs: 'column', sm: 'row' },
                      justifyContent: isTablet ? 'center' : { xs: 'center', sm: 'flex-start' },
                    }}
                  >
                    {/* Step Number Box */}
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: isTablet ? 42 : { xs: 36, sm: 38, md: 40 },
                        height: isTablet ? 42 : { xs: 36, sm: 38, md: 40 },
                        borderRadius: 1,
                        bgcolor: isCompleted
                          ? theme.palette.mode === 'dark'
                            ? 'rgba(34, 197, 94, 0.8)' // Slightly transparent green for dark mode
                            : '#22C55E' // Green color for completed steps
                          : isActive
                            ? theme.palette.mode === 'dark'
                              ? 'rgba(255, 111, 60, 0.8)' // Slightly transparent orange for dark mode
                              : '#FF6F3C' // Orange color for active step
                            : theme.palette.mode === 'dark'
                              ? 'rgba(255, 255, 255, 0.15)' // Very light grey for dark mode
                              : 'grey.300',
                        color: 'common.white',
                        zIndex: 1,
                        position: 'relative',
                        boxShadow: isActive
                          ? theme.palette.mode === 'dark'
                            ? '0 0 0 4px rgba(255, 111, 60, 0.3)'
                            : '0 0 0 4px rgba(255, 111, 60, 0.2)'
                          : 'none',
                        mr: !developerMode ? 2 : isTablet ? 0 : { xs: 0, sm: 1.5, md: 2 }, // Adjust margin for client mode
                        mb: isTablet ? 1 : { xs: 1, sm: 0 }, // Always add bottom margin on tablet
                        transition: 'all 0.2s ease-in-out',
                      }}
                    >
                      {isCompleted ? (
                        <Iconify icon="eva:checkmark-fill" width={20} height={20} />
                      ) : (
                        <Typography
                          variant="subtitle1"
                          fontWeight="bold"
                          sx={{
                            fontSize: isTablet ? '1rem' : { xs: '0.875rem', sm: '1rem' },
                          }}
                        >
                          {index + 1}
                        </Typography>
                      )}
                    </Box>

                    {/* Step Label and Description */}
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: isTablet ? 'center' : { xs: 'center', sm: 'flex-start' },
                        textAlign: isTablet ? 'center' : { xs: 'center', sm: 'left' },
                        width: isTablet ? '100%' : 'auto',
                      }}
                    >
                      <Typography
                        variant="subtitle2"
                        sx={{
                          color: isCompleted
                            ? theme.palette.mode === 'dark'
                              ? 'rgba(34, 197, 94, 0.9)' // Slightly transparent green for dark mode
                              : '#22C55E' // Green color for completed steps
                            : isActive
                              ? theme.palette.mode === 'dark'
                                ? 'rgba(255, 111, 60, 0.9)' // Slightly transparent orange for dark mode
                                : '#FF6F3C' // Orange color for active step
                              : 'text.primary',
                          fontWeight: isActive || isCompleted ? 600 : 400,
                          fontSize: { xs: '0.8rem', sm: '0.875rem', md: '1rem' },
                          whiteSpace: 'nowrap',
                          textTransform: 'capitalize',
                        }}
                      >
                        {step.label}
                      </Typography>
                      <Typography
                        variant="caption"
                        sx={{
                          color:
                            theme.palette.mode === 'dark'
                              ? 'rgba(255, 255, 255, 0.7)' // More visible text in dark mode
                              : 'text.secondary',
                          textAlign: isTablet ? 'center' : { xs: 'center', sm: 'left' },
                          maxWidth: isTablet ? '100%' : { xs: '100%', sm: 120, md: 140 },
                          fontSize: { xs: '0.7rem', sm: '0.75rem' },
                          display: isTablet ? 'block' : { xs: 'none', sm: 'block' }, // Show on tablet
                          mt: 0.5,
                        }}
                      >
                        {step.description}
                      </Typography>
                    </Box>
                  </Box>
                </Step>
              );
            })}
          </Stepper>
        )}
      </Box>

      {/* Form Fields */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={activeStep === 0 ? 7 : 12}>
          <Box sx={{ mt: 2 }}>{steps[activeStep].fields}</Box>

          <Box
            sx={{
              mt: '24px',
            }}
          >
            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              spacing={{ xs: 1.5, sm: 2 }}
              sx={{
                width: '100%',
                '& .MuiButton-root': {
                  minHeight: { xs: 44, sm: 'auto' },
                },
              }}
            >
              <AppButton
                fullWidth={isMobile}
                label={t('buttons.cancel')}
                variant="outlined"
                color="inherit"
                onClick={(_event) => {
                  _event.preventDefault(); // Prevent form submission
                  // Reset form state before navigating
                  resetForm();
                  // Call the cancel handler
                  onCancel();
                }}
                sx={{
                  order: { xs: 3, sm: 1 },
                  display: isMobile && activeStep > 0 ? 'none' : 'flex',
                }}
              />

              {activeStep > 0 && (
                <AppButton
                  fullWidth={isMobile}
                  label={t('buttons.previous')}
                  variant="outlined"
                  onClick={(_event) => {
                    _event.preventDefault(); // Prevent form submission
                    handleBack();
                  }}
                  sx={{
                    order: { xs: 2, sm: 2 },
                    display: isMobile ? 'none' : 'flex', // Hide on mobile as we have arrow buttons
                  }}
                />
              )}

              {/* Next button - only show if not on last step */}
              {activeStep < TEAM_TEMPLATE_FORM_STEPS.length - 1 && (
                <AppButton
                  fullWidth={isMobile}
                  label={t('buttons.next')}
                  variant="contained"
                  onClick={(_event) => {
                    _event.preventDefault(); // Prevent form submission
                    handleNext();
                  }}
                  sx={{
                    order: { xs: 1, sm: 3 },
                    display: isMobile ? 'none' : 'flex', // Hide on mobile as we have arrow buttons
                  }}
                />
              )}

              {/* Create Team button - only show on last step */}
              {activeStep === TEAM_TEMPLATE_FORM_STEPS.length - 1 && (
                <AppButton
                  fullWidth={isMobile}
                  label={t('pages.teams.createTeam')}
                  variant="contained"
                  isLoading={isSubmitting}
                  onClick={(_event) => {
                    // Manually handle form submission
                    _event.preventDefault();
                    // Get form data
                    const _formData = methods.getValues();
                    // Reset form state before navigating
                    resetForm();
                    // Submit the form data
                    onSubmit(_formData);
                  }}
                  sx={{
                    order: { xs: 1, sm: 3 },
                  }}
                />
              )}
            </Stack>
          </Box>
        </Grid>

        {activeStep === 0 && (
          <Grid item xs={12} md={5}>
            {/* Use memoized component to prevent re-rendering when flow control changes */}
            {memoizedOverviewCard}
          </Grid>
        )}
      </Grid>
    </Form>
  );
}
