import type { Theme, Components } from '@mui/material/styles';

import { inputLabelClasses } from '@mui/material/InputLabel';

// ----------------------------------------------------------------------

const Mu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: Components<Theme>['MuiFormLabel'] = {
  /** **************************************
   * STYLE
   *************************************** */
  styleOverrides: {
    root: ({ theme }) => ({
      ...theme.typography.body2,
      color: theme.vars.palette.text.disabled,
      // Make label white in dark mode
      [theme.getColorSchemeSelector('dark')]: {
        color: theme.vars.palette.common.white,
      },
      [`&.${inputLabelClasses.shrink}`]: {
        ...theme.typography.body1,
        fontWeight: 600,
        color: theme.vars.palette.text.secondary,
        // Make shrunk label white in dark mode
        [theme.getColorSchemeSelector('dark')]: {
          color: theme.vars.palette.common.white,
        },
        [`&.${inputLabelClasses.focused}`]: {
          color: theme.vars.palette.text.primary,
          // Make focused label white in dark mode
          [theme.getColorSchemeSelector('dark')]: {
            color: theme.vars.palette.common.white,
          },
        },
        [`&.${inputLabelClasses.error}`]: { color: theme.vars.palette.error.main },
        [`&.${inputLabelClasses.disabled}`]: { color: theme.vars.palette.text.disabled },
        [`&.${inputLabelClasses.filled}`]: { transform: 'translate(12px, 6px) scale(0.75)' },
      },
    }),
  },
};

// ----------------------------------------------------------------------

const MuiFormHelperText: Components<Theme>['MuiFormHelperText'] = {
  /** **************************************
   * DEFAULT PROPS
   *************************************** */
  defaultProps: { component: 'div' },

  /** **************************************
   * STYLE
   *************************************** */
  styleOverrides: {
    root: ({ theme }) => ({
      marginTop: theme.spacing(1),
      // Make helper text more visible in dark mode
      [theme.getColorSchemeSelector('dark')]: {
        color: theme.vars.palette.grey[400],
      },
    }),
  },
};

// ----------------------------------------------------------------------

const MuiFormControlLabel: Components<Theme>['MuiFormControlLabel'] = {
  /** **************************************
   * STYLE
   *************************************** */
  styleOverrides: {
    label: ({ theme }) => ({
      ...theme.typography.body2,
      // Make label white in dark mode
      [theme.getColorSchemeSelector('dark')]: {
        color: theme.vars.palette.common.white,
      },
    }),
  },
};

// ----------------------------------------------------------------------

export const form = { MuiFormLabel, MuiFormHelperText, MuiFormControlLabel };
