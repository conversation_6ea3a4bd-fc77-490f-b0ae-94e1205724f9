import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON>le,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  Button,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Stack,
  TextField,
} from '@mui/material';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';

// ----------------------------------------------------------------------

// Define the form schema with validation rules
const connectAPISchema = z.object({
  url: z.string().url('Please enter a valid URL'),
  method: z.string().min(1, 'Method is required'),
  authType: z.string().min(1, 'Authentication type is required'),
});

type ConnectAPIFormValues = z.infer<typeof connectAPISchema>;

// Header interface
interface Header {
  id: string;
  key: string;
  value: string;
  isEditing: boolean;
}

// ----------------------------------------------------------------------

type ConnectAPIDialogProps = {
  open: boolean;
  onClose: () => void;
};

export default function ConnectAPIDialog({ open, onClose }: ConnectAPIDialogProps) {
  const [headers, setHeaders] = useState<Header[]>([
    { id: 'header-1', key: 'Authorization', value: 'Bearer token123', isEditing: false },
    { id: 'header-2', key: 'Content-Type', value: 'application/json', isEditing: false },
    { id: 'header-3', key: 'Accept', value: 'application/xml', isEditing: false },
  ]);

  // Initialize form with default values
  const methods = useForm<ConnectAPIFormValues>({
    resolver: zodResolver(connectAPISchema),
    defaultValues: {
      url: '',
      method: 'GET',
      authType: 'None',
    },
  });

  const { handleSubmit } = methods;

  const onSubmit = async (data: ConnectAPIFormValues) => {
    try {
      console.log('Form data submitted:', data);
      console.log('Headers:', headers);
      // In a real app, you would make an API call here
      onClose();
    } catch (error) {
      console.error('Error connecting to API:', error);
    }
  };

  const handleRemoveHeader = (headerId: string) => {
    setHeaders((prev) => prev.filter((header) => header.id !== headerId));
  };

  const handleAddHeader = () => {
    const newHeader: Header = {
      id: `header-${Date.now()}`,
      key: '',
      value: '',
      isEditing: true,
    };
    setHeaders((prev) => [...prev, newHeader]);
  };

  const handleHeaderKeyChange = (headerId: string, value: string) => {
    setHeaders((prev) =>
      prev.map((header) => (header.id === headerId ? { ...header, key: value } : header))
    );
  };

  const handleHeaderValueChange = (headerId: string, value: string) => {
    setHeaders((prev) =>
      prev.map((header) => (header.id === headerId ? { ...header, value } : header))
    );
  };

  const handleSaveHeader = (headerId: string) => {
    setHeaders((prev) =>
      prev.map((header) => (header.id === headerId ? { ...header, isEditing: false } : header))
    );
  };

  const handleEditHeader = (headerId: string) => {
    setHeaders((prev) =>
      prev.map((header) => (header.id === headerId ? { ...header, isEditing: true } : header))
    );
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      PaperProps={{
        sx: {
          borderRadius: 4,
          padding: 2,
          width: { xs: '90%', sm: '600px' },
          mx: 'auto',
        },
      }}
    >
      {/* Close button */}
      <IconButton
        onClick={onClose}
        sx={{
          position: 'absolute',
          right: 16,
          top: 16,
          color: 'grey.500',
          bgcolor: 'grey.100',
          '&:hover': {
            bgcolor: 'grey.200',
          },
          width: 36,
          height: 36,
          zIndex: 1,
        }}
      >
        <Iconify icon="eva:close-fill" width={20} height={20} />
      </IconButton>

      {/* Dialog content */}
      <DialogTitle sx={{ textAlign: 'center', pt: 4, pb: 2 }}>
        <Typography variant="h3" component="div">
          Connect an API
        </Typography>
      </DialogTitle>

      <DialogContent>
        <Typography variant="body1" color="text.secondary" align="center" sx={{ mb: 4 }}>
          Securely link your database from different types
        </Typography>

        <Form methods={methods} onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={3}>
            <Field.Text name="url" label="URL" placeholder="https://api.example.com/v1" />

            <Field.Select
              name="method"
              label="Method"
              options={[
                { value: 'GET', label: 'GET' },
                { value: 'POST', label: 'POST' },
                { value: 'PUT', label: 'PUT' },
                { value: 'DELETE', label: 'DELETE' },
                { value: 'PATCH', label: 'PATCH' },
              ]}
            />

            <Field.Select
              name="authType"
              label="Auth Type"
              options={[
                { value: 'None', label: 'None' },
                { value: 'Basic', label: 'Basic Auth' },
                { value: 'Bearer', label: 'Bearer Token' },
                { value: 'API Key', label: 'API Key' },
                { value: 'OAuth 2.0', label: 'OAuth 2.0' },
              ]}
            />

            {/* Headers Table */}
            <Box sx={{ mt: 2 }}>
              <TableContainer sx={{ border: '1px solid #e0e0e0', borderRadius: 1 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ fontWeight: 'bold' }}>Header key</TableCell>
                      <TableCell sx={{ fontWeight: 'bold' }}>Header value</TableCell>
                      <TableCell sx={{ fontWeight: 'bold' }}>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {headers.map((header) => (
                      <TableRow key={header.id}>
                        <TableCell>
                          {header.isEditing ? (
                            <TextField
                              size="small"
                              value={header.key}
                              onChange={(e) => handleHeaderKeyChange(header.id, e.target.value)}
                              placeholder="Enter key"
                              fullWidth
                              variant="outlined"
                              sx={{ my: -1 }}
                            />
                          ) : (
                            header.key
                          )}
                        </TableCell>
                        <TableCell>
                          {header.isEditing ? (
                            <TextField
                              size="small"
                              value={header.value}
                              onChange={(e) => handleHeaderValueChange(header.id, e.target.value)}
                              placeholder="Enter value"
                              fullWidth
                              variant="outlined"
                              sx={{ my: -1 }}
                            />
                          ) : (
                            header.value
                          )}
                        </TableCell>
                        <TableCell>
                          <Stack direction="row" spacing={1}>
                            {header.isEditing ? (
                              <Button
                                color="primary"
                                onClick={() => handleSaveHeader(header.id)}
                                sx={{ minWidth: 'auto', p: 0, color: '#FF6B35' }}
                              >
                                Save
                              </Button>
                            ) : (
                              <Button
                                color="primary"
                                onClick={() => handleEditHeader(header.id)}
                                sx={{ minWidth: 'auto', p: 0, color: '#FF6B35' }}
                              >
                                Edit
                              </Button>
                            )}
                            <Button
                              color="error"
                              onClick={() => handleRemoveHeader(header.id)}
                              sx={{ minWidth: 'auto', p: 0 }}
                            >
                              Remove
                            </Button>
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow>
                      <TableCell colSpan={3}>
                        <Button
                          startIcon={<Iconify icon="eva:plus-fill" />}
                          onClick={handleAddHeader}
                          sx={{ color: '#FF6B35' }}
                        >
                          Add header
                        </Button>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          </Stack>

          <DialogActions sx={{ justifyContent: 'center', gap: 2, pb: 3, pt: 3 }}>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              sx={{
                bgcolor: '#FF6B35',
                '&:hover': { bgcolor: '#E55A2A' },
                borderRadius: 1,
                minWidth: 160,
              }}
            >
              Connect
            </Button>
            <Button
              variant="outlined"
              color="inherit"
              onClick={onClose}
              sx={{ minWidth: 160, borderRadius: 1 }}
            >
              Back
            </Button>
          </DialogActions>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
