import { Grid, <PERSON>ack, Avatar, Typography, Box, Divider } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Label } from 'src/components/label';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import { TeamData, TeamMember } from './use-teams-view';
import { StyledTeamCard } from '../components/styled-team-card';

interface SearchResultsViewProps {
  searchQuery: string;
  teams: TeamData[];
  members: TeamMember[];
  onUseTemplate: (teamId: string) => void;
}

export const SearchResultsView = ({
  searchQuery,
  teams,
  members,
  onUseTemplate,
}: SearchResultsViewProps) => {
  const { t, i18n } = useTranslation();
  const isRtl = i18n.language === 'ar';

  return (
    <Box sx={{ width: '100%', direction: isRtl ? 'rtl' : 'ltr' }}>
      <Typography variant="h4" sx={{ mb: 3, textAlign: isRtl ? 'right' : 'left' }}>
        {t('pages.teams.searchResults')}
      </Typography>
      <Typography
        variant="body1"
        color="text.secondary"
        sx={{ mb: 4, textAlign: isRtl ? 'right' : 'left' }}
      >
        {t('pages.teams.searchResultsFor', { query: searchQuery })}
      </Typography>

      {/* Teams Results */}
      {teams.length > 0 && (
        <Grid container spacing={3} sx={{ mb: 5 }}>
          {teams.map((team) => (
            <Grid item xs={12} sm={12} md={4} key={team.id}>
              <StyledTeamCard onClick={() => onUseTemplate(team.id)}>
                <Stack spacing={2} sx={{ flexGrow: 1 }}>
                  {/* Team Name and Type */}
                  <Stack
                    direction="row"
                    justifyContent="space-between"
                    alignItems="center"
                    spacing={1}
                    sx={{ direction: isRtl ? 'rtl' : 'ltr' }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="h6" noWrap>
                        {team.name}
                      </Typography>
                      <Label
                        variant="soft"
                        color="warning"
                        sx={{ textTransform: 'capitalize', fontWeight: 'bold' }}
                      >
                        {team.type}
                      </Label>
                    </Box>
                    <Stack direction="row" alignItems="center" spacing={0.5}>
                      <Iconify
                        icon="ri:box-3-fill"
                        sx={{ color: 'grayText', width: 24, height: 24 }}
                      />
                      <Typography variant="subtitle2" sx={{ color: 'text.secondary' }}>
                        {team.userCount || 0}
                      </Typography>
                    </Stack>
                  </Stack>

                  {/* Team Description */}
                  <Typography
                    variant="body2"
                    sx={{
                      color: 'text.secondary',
                      flexGrow: 1,
                      textAlign: isRtl ? 'right' : 'left',
                      direction: isRtl ? 'rtl' : 'ltr',
                    }}
                  >
                    {team.description}
                  </Typography>
                </Stack>

                {/* Card Footer - Avatars and Use Template Button */}
                <Stack
                  direction="row"
                  justifyContent="space-between"
                  alignItems="center"
                  sx={{
                    mt: 2,
                    pt: 2,
                    direction: isRtl ? 'rtl' : 'ltr',
                  }}
                >
                  <Stack direction="row" spacing={-0.5}>
                    {team.members.slice(0, 3).map((member) => (
                      <Avatar
                        key={member.id}
                        alt={member.name}
                        src={member.avatarUrl}
                        title={member.name}
                        sx={{
                          width: 32,
                          height: 32,
                          fontSize: '0.875rem',
                          border: (theme) => `2px solid ${theme.palette.background.paper}`,
                        }}
                      />
                    ))}
                    {team.members.length > 3 && (
                      <Avatar
                        sx={{
                          width: 32,
                          height: 32,
                          fontSize: '0.875rem',
                          bgcolor: 'primary.main',
                          border: (theme) => `2px solid ${theme.palette.background.paper}`,
                        }}
                      >
                        +{team.members.length - 3}
                      </Avatar>
                    )}
                  </Stack>

                  <AppButton
                    label={t('pages.teams.useTemplate')}
                    fullWidth={false}
                    variant="outlined"
                    size="small"
                    color="primary"
                    onClick={(e) => {
                      e.stopPropagation();
                      onUseTemplate(team.id);
                    }}
                    sx={{
                      px: 2,
                    }}
                  />
                </Stack>
              </StyledTeamCard>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Agents Results */}
      {members.length > 0 && (
        <>
          <Typography variant="h5" sx={{ mb: 2 }}>
            {t('pages.teams.agents')}
          </Typography>
          <Grid container spacing={2}>
            {members.map((member) => (
              <Grid item xs={12} key={member.id}>
                <StyledTeamCard sx={{ p: 2 }}>
                  <Stack
                    direction="row"
                    alignItems="center"
                    spacing={2}
                    sx={{ direction: isRtl ? 'rtl' : 'ltr' }}
                  >
                    <Avatar
                      src={member.avatarUrl}
                      alt={member.name}
                      sx={{ width: 48, height: 48 }}
                    />
                    <Box>
                      <Typography
                        variant="subtitle1"
                        fontWeight="bold"
                        sx={{ textAlign: isRtl ? 'right' : 'left' }}
                      >
                        {member.name}
                      </Typography>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ textAlign: isRtl ? 'right' : 'left' }}
                      >
                        {member.role}
                      </Typography>
                    </Box>
                    <Box sx={{ flexGrow: 1 }} />
                    <Label variant="soft" color="info">
                      {t('pages.teams.teamMember')}
                    </Label>
                  </Stack>
                </StyledTeamCard>
              </Grid>
            ))}
          </Grid>
        </>
      )}

      {teams.length === 0 && members.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 5, direction: isRtl ? 'rtl' : 'ltr' }}>
          <Iconify
            icon="eva:search-fill"
            width={64}
            height={64}
            sx={{ color: 'text.disabled', mb: 2 }}
          />
          <Typography variant="h6" paragraph>
            {t('pages.teams.noSearchResults')}
          </Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            {t('pages.teams.tryDifferentSearch')}
          </Typography>
        </Box>
      )}
    </Box>
  );
};
