import { useEffect, useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';

import { _account } from 'src/layouts/config-nav-account';
import { _notifications } from 'src/_mock/_notifications';
import { AccountMenu } from 'src/layouts/components/account-menu';
import { NotificationsMenu } from 'src/layouts/components/notifications-menu';
import { TeamMembersDialog } from 'src/sections/teams/chat/team-members-dialog';

import {
  Typography,
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  IconButton,
  useTheme,
  useMediaQuery,
  InputBase,
  Paper,
  Avatar,
  AvatarGroup,
  Button,
  TextField,
  alpha,
} from '@mui/material';

// import { AppContainer } from 'src/components/common';
import { TeamChat } from 'src/sections/teams/chat/team-chat';
import { useTeamChat } from 'src/sections/teams/chat/use-team-chat';
import { Iconify } from 'src/components/iconify';
import { Logo } from 'src/components/logo';
import { AppButton } from 'src/components/common';

// ----------------------------------------------------------------------

// Mock data for previous conversations
interface Conversation {
  id: string;
  name: string;
  timestamp: Date;
}

const MOCK_CONVERSATIONS: Conversation[] = [
  {
    id: 'conv1',
    name: 'New chat',
    timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
  },
  {
    id: 'conv2',
    name: 'UI/UX explained for beginners',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3), // 3 hours ago
  },
];

// Mock team members
interface TeamMember {
  id: string;
  name: string;
  avatar: string;
}

const MOCK_TEAM_MEMBERS: TeamMember[] = [
  {
    id: 'member1',
    name: 'John Doe',
    avatar: '/assets/images/avatars/avatar_1.jpg',
  },
  {
    id: 'member2',
    name: 'Jane Smith',
    avatar: '/assets/images/avatars/avatar_2.jpg',
  },
  {
    id: 'member3',
    name: 'Mike Johnson',
    avatar: '/assets/images/avatars/avatar_3.jpg',
  },
  {
    id: 'member4',
    name: 'Sarah Williams',
    avatar: '/assets/images/avatars/avatar_4.jpg',
  },
];

export default function ChatPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { t } = useTranslation();

  const [teamName, setTeamName] = useState('AI Team');
  const [drawerOpen, setDrawerOpen] = useState(!isMobile);
  const [conversations, setConversations] = useState<Conversation[]>(MOCK_CONVERSATIONS);
  const [credits] = useState(1462); // Credits are currently read-only
  const [inputValue, setInputValue] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [membersDialogOpen, setMembersDialogOpen] = useState(false);

  const { messages, isTyping, handleSendMessage, teamMembers, clearMessages } =
    useTeamChat(teamName);

  // Notification handlers
  const handleMarkAllAsRead = () => {
    console.log('Chat page: Marking all notifications as read');
    // In a real app, you would call an API to mark all notifications as read
  };

  const handleViewAllNotifications = () => {
    console.log('Chat page: Navigate to all notifications page');
    // In a real app, you would navigate to the notifications page
  };

  // Fetch team details (in a real app, this would be an API call)
  useEffect(() => {
    // Simulate API call to get team details
    const fetchTeamDetails = async () => {
      // Try to get the team name from session storage first
      const storedTeamName = sessionStorage.getItem('teamName');

      if (storedTeamName) {
        setTeamName(storedTeamName);
        // Clear the session storage to avoid using stale data
        sessionStorage.removeItem('teamName');
      } else {
        // In a real app, you would fetch the team details from an API
        // For now, we'll use a fixed team name
        setTeamName('AI Team');
      }
    };

    fetchTeamDetails();
  }, [id]);

  // Handle creating a new chat
  const handleNewChat = () => {
    // Create a new conversation ID
    const newChatId = `new_${Date.now()}`;

    // Add the new chat to the conversations list
    const newChat = {
      id: newChatId,
      name: 'New chat',
      timestamp: new Date(),
    };

    // Update conversations list with the new chat at the top
    setConversations([newChat, ...conversations]);

    // Clear any existing messages
    clearMessages();

    // Reset input value
    setInputValue('');

    // Navigate to the new chat
    navigate(`/dashboard/teams/chat/${newChatId}`);
  };

  // Handle selecting a conversation
  const handleSelectConversation = (conversationId: string) => {
    // In a real app, you would load the conversation from the database
    // For now, we'll just navigate to the conversation
    navigate(`/dashboard/teams/chat/${conversationId}`);
  };

  // Calculate drawer width
  const drawerWidth = 340;

  return (
    <>
      <Helmet>
        <title>
          {t('pages.teams.chatWith')} {teamName} | Midad AI
        </title>
      </Helmet>

      <Box
        sx={{
          display: 'flex',
          height: '100vh',
          background: (theme) => (theme.palette.mode === 'dark' ? 'background.default' : 'white'),
        }}
      >
        {/* Sidebar */}
        <Drawer
          variant={isMobile ? 'temporary' : 'persistent'}
          open={drawerOpen}
          onClose={() => setDrawerOpen(false)}
          sx={{
            width: drawerWidth,
            flexShrink: 0,
            '& .MuiDrawer-paper': {
              width: drawerWidth,
              boxSizing: 'border-box',
              borderRight: '1px solid',
              borderColor: 'divider',
              background: (theme) =>
                theme.palette.mode === 'dark'
                  ? 'linear-gradient(to top, rgba(255, 111, 60, 0.3) 5%, rgba(255, 111, 60, 0.2) 20%, rgba(30, 30, 30, 1) 40%)'
                  : 'linear-gradient(to top, rgba(255, 111, 60, 0.5) 5%, rgba(255, 111, 60, 0.3) 20%, rgba(255, 255, 255, 1) 40%)',
              display: 'flex',
              flexDirection: 'column',
              height: '100%',
              overflow: 'hidden',
            },
          }}
        >
          {/* Fixed Top Section */}
          <Box
            sx={{
              flexShrink: 0,
              borderBottom: (theme) =>
                `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'}`,
              pb: 1,
            }}
          >
            {/* Logo */}
            <Box
              sx={{
                p: 0.5,
                ml: '10px',
                display: 'flex',
                alignItems: 'center',
                gap: '5px',
                mb: 2,
              }}
            >
              <Logo data-slot="logo" />
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 'bold',
                  color: (theme) => (theme.palette.mode === 'dark' ? 'white' : '#333'),
                  mt: '25px',
                }}
              >
                Workforces
              </Typography>
            </Box>

            {/* Search */}
            <Box sx={{ px: 2, py: 1 }}>
              <Paper
                component="form"
                sx={{
                  p: '2px 8px',
                  display: 'flex',
                  alignItems: 'center',
                  borderRadius: 2,
                  bgcolor: (theme) =>
                    theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : '#F5F5F5',
                }}
              >
                <IconButton sx={{ p: '10px' }} aria-label="search">
                  <Iconify
                    icon="eva:search-fill"
                    width={20}
                    sx={{ color: (theme) => (theme.palette.mode === 'dark' ? 'white' : 'inherit') }}
                  />
                </IconButton>
                <InputBase
                  sx={{
                    ml: 1,
                    flex: 1,
                    color: (theme) => (theme.palette.mode === 'dark' ? 'white' : 'inherit'),
                    '& ::placeholder': {
                      color: (theme) =>
                        theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.5)' : 'inherit',
                      opacity: 1,
                    },
                  }}
                  placeholder="Search"
                />
              </Paper>
            </Box>

            {/* New Chat Button */}
            <Box sx={{ px: 2, py: 1 }}>
              <ListItemButton
                onClick={handleNewChat}
                sx={{
                  borderRadius: 1,
                  mb: 1,
                  bgcolor: (theme) =>
                    theme.palette.mode === 'dark'
                      ? alpha(theme.palette.primary.main, 0.15)
                      : '#FEE4DC',
                  '&:hover': {
                    bgcolor: (theme) =>
                      theme.palette.mode === 'dark'
                        ? alpha(theme.palette.primary.main, 0.25)
                        : '#FFDACB',
                  },
                  py: 1,
                }}
              >
                <Typography
                  sx={{
                    color: (theme) => (theme.palette.mode === 'dark' ? 'white' : '#333'),
                  }}
                >
                  {t('pages.teams.newChat')}
                </Typography>
              </ListItemButton>
            </Box>
          </Box>

          {/* Scrollable Conversation List */}
          <Box
            sx={{
              flexGrow: 1,
              overflowY: 'auto',
              '&::-webkit-scrollbar': {
                width: '6px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: (theme) =>
                  theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
                borderRadius: '3px',
              },
              '&::-webkit-scrollbar-track': {
                backgroundColor: 'transparent',
              },
              // Add padding at the bottom to ensure last items are visible above the credits box
              pb: 2,
            }}
          >
            <List sx={{ p: 0, px: 2 }}>
              {conversations.map((conversation) => (
                <ListItem key={conversation.id} disablePadding sx={{ mb: 1 }}>
                  <ListItemButton
                    onClick={() => handleSelectConversation(conversation.id)}
                    selected={conversation.id === id}
                    sx={{
                      borderRadius: 1,
                      py: 1,
                      '&.Mui-selected': {
                        bgcolor: (theme) =>
                          theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : '#F5F5F5',
                        '&:hover': {
                          bgcolor: (theme) =>
                            theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : '#EEEEEE',
                        },
                      },
                      '&:hover': {
                        bgcolor: (theme) =>
                          theme.palette.mode === 'dark'
                            ? 'rgba(255, 255, 255, 0.05)'
                            : 'rgba(0, 0, 0, 0.04)',
                      },
                    }}
                  >
                    <ListItemText
                      primary={conversation.name}
                      primaryTypographyProps={{
                        noWrap: true,
                        fontSize: '0.875rem',
                        color: (theme) => (theme.palette.mode === 'dark' ? 'white' : '#333'),
                      }}
                    />
                  </ListItemButton>
                </ListItem>
              ))}
            </List>
          </Box>

          {/* Fixed Credits Box at Bottom */}

          <Box
            sx={{
              flexShrink: 0,

              backgroundColor: (theme) =>
                theme.palette.mode === 'dark'
                  ? 'linear-gradient(135deg, rgba(255, 107, 53, 0.15) 0%, rgba(255, 107, 53, 0.05) 100%)'
                  : 'white',
              borderRadius: 4,
              mx: 2,
              mb: 2,
              mt: 1,
              boxShadow: (theme) =>
                theme.palette.mode === 'dark'
                  ? '0 2px 16px rgba(0,0,0,0.2)'
                  : '0 2px 16px rgba(0,0,0,0.02)',
              position: 'relative',
              overflow: 'hidden',
              border: (theme) =>
                theme.palette.mode === 'dark'
                  ? '1px solid rgba(255, 107, 53, 0.2)'
                  : '1px solid rgba(255, 107, 53, 0.08)',
            }}
          >
            <Box
              sx={{
                p: 3,
                width: '100%',
                height: '100%',
                backgroundImage: 'url(/assets/background/back-ground-sidabar-chat.png)',
              }}
            >
              {/* Package Icon */}
              <Box
                sx={{
                  position: 'absolute',
                  top: 18,
                  right: 40,
                  width: 32,
                  height: 32,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#FF6B35',
                  zIndex: 2,
                }}
              >
                <Iconify icon="fluent:box-24-filled" width={24} height={24} />
              </Box>

              {/* Dotted Line */}
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  right: 10,
                  width: 100,
                  height: 80,
                  borderRight: (theme) =>
                    `2.5px dashed ${
                      theme.palette.mode === 'dark'
                        ? 'rgba(255, 107, 53, 0.4)'
                        : 'rgba(255, 107, 53, 0.25)'
                    }`,
                  borderTop: (theme) =>
                    `2.5px dashed ${
                      theme.palette.mode === 'dark'
                        ? 'rgba(255, 107, 53, 0.4)'
                        : 'rgba(255, 107, 53, 0.25)'
                    }`,
                  borderTopRightRadius: 80,
                  transform: 'rotate(-185deg)',
                }}
              />

              <Typography
                variant="h4"
                sx={{
                  color: '#FF6B35',
                  fontWeight: 'bold',
                  mb: 0.5,
                  fontSize: '1.75rem',
                  letterSpacing: '-0.5px',
                }}
              >
                {credits.toLocaleString()}
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: (theme) =>
                    theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.6)',
                  display: 'block',
                  mb: 2,
                  fontSize: '0.875rem',
                }}
              >
                Credits available
              </Typography>
              <AppButton
                label="Get More"
                fullWidth
                variant="contained"
                sx={{
                  bgcolor:
                    theme.palette.mode === 'dark'
                      ? alpha(theme.palette.primary.main, 0.9)
                      : '#FF6B35',
                  '&:hover': {
                    bgcolor: theme.palette.mode === 'dark' ? theme.palette.primary.main : '#E55A2A',
                  },

                  textTransform: 'none',
                  fontWeight: 600,
                  py: 1.5,
                  fontSize: '1rem',
                  boxShadow:
                    theme.palette.mode === 'dark' ? '0 0 10px rgba(255, 107, 53, 0.3)' : 'none',
                  height: 48,
                  color: 'white',
                }}
              />
            </Box>
          </Box>
        </Drawer>

        {/* Main content */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            p: 0,
            width: { sm: `calc(100% - ${drawerWidth}px)` },
            // ml: { sm: `${drawerWidth}px` },
            display: 'flex',
            flexDirection: 'column',
            // background: (theme) =>
            //   theme.palette.mode === 'dark'
            //     ? 'linear-gradient(to bottom, rgba(30, 30, 30, 1) 0%, rgba(255, 111, 60, 0.05) 90%, rgba(255, 111, 60, 0.1) 100%)'
            //     : '#FFFFFF',
            position: 'relative',
            borderLeft: '1px solid',
            borderColor: 'divider',
          }}
        >
          {/* Chat header */}
          <Box
            sx={{
              py: 2,
              px: 3,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              background: (theme) =>
                theme.palette.mode === 'dark' ? 'rgba(30, 30, 30, 0.8)' : 'white',
              backdropFilter: (theme) => (theme.palette.mode === 'dark' ? 'blur(8px)' : 'none'),
              borderBottom: '1px solid',
              borderColor: 'divider',
              position: 'relative',
              zIndex: 5,
            }}
          >
            {/* Left side - Team info */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {isMobile && (
                <IconButton
                  edge="start"
                  color="inherit"
                  aria-label="menu"
                  onClick={() => setDrawerOpen(!drawerOpen)}
                  sx={{ mr: 2 }}
                >
                  <Iconify icon="eva:menu-2-fill" />
                </IconButton>
              )}
              <IconButton sx={{ mr: 1 }} onClick={() => navigate('/dashboard/teams')}>
                <Iconify icon="eva:arrow-back-fill" />
              </IconButton>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  cursor: 'pointer',
                  '&:hover': {
                    opacity: 0.8,
                  },
                }}
                onClick={() => setMembersDialogOpen(true)}
              >
                <Avatar
                  sx={{
                    bgcolor: (theme) =>
                      theme.palette.mode === 'dark'
                        ? alpha(theme.palette.primary.main, 0.2)
                        : '#FEE4DC',
                    color: '#FF6B35',
                    width: 32,
                    height: 32,
                    mr: 1,
                    fontSize: '0.875rem',
                  }}
                >
                  AI
                </Avatar>
                <Box>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: 'bold',
                      color: (theme) => (theme.palette.mode === 'dark' ? 'white' : '#333'),
                    }}
                  >
                    {teamName}
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{
                      color: (theme) =>
                        theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.7)' : '#666',
                    }}
                  >
                    {teamMembers.length} members
                  </Typography>
                </Box>
              </Box>
            </Box>

            {/* Right side - Icons */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              {/* Notifications menu */}
              <NotificationsMenu
                data-slot="notifications"
                data={_notifications}
                onMarkAllAsRead={handleMarkAllAsRead}
                onViewAll={handleViewAllNotifications}
              />

              {/* Account menu */}
              <AccountMenu data-slot="account" data={_account} />
            </Box>
          </Box>

          {/* Chat component */}
          {messages.length === 0 ? (
            <Box
              sx={{
                flexGrow: 1,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                p: 3,
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: (theme) =>
                    theme.palette.mode === 'dark'
                      ? 'radial-gradient(circle at center, rgba(255, 111, 60, 0.05) 0%, transparent 70%)'
                      : 'transparent',
                  zIndex: 0,
                },
              }}
            >
              {/* Simple Team Welcome */}
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  textAlign: 'center',
                  maxWidth: 400,
                  position: 'relative',
                  zIndex: 1,
                  background: (theme) =>
                    theme.palette.mode === 'dark' ? 'rgba(30, 30, 30, 0.5)' : 'white',
                  backdropFilter: (theme) =>
                    theme.palette.mode === 'dark' ? 'blur(10px)' : 'none',
                  borderRadius: 3,
                  p: 4,
                }}
              >
                {/* Team Avatars */}
                <AvatarGroup max={4}>
                  {teamMembers.map((member) => (
                    <Avatar key={member.id} src={member.avatar} />
                  ))}
                  <Avatar
                    sx={{
                      bgcolor: (theme) =>
                        theme.palette.mode === 'dark'
                          ? alpha(theme.palette.primary.main, 0.2)
                          : '#FEE4DC',
                      color: '#FF6B35',
                    }}
                  >
                    +2
                  </Avatar>
                </AvatarGroup>

                {/* Team Name */}
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: 'bold',
                    mb: 1,
                    color: (theme) => (theme.palette.mode === 'dark' ? 'white' : '#333'),
                  }}
                >
                  {teamName}
                </Typography>

                {/* Team Description */}
                <Typography
                  variant="body2"
                  sx={{
                    color: (theme) =>
                      theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.7)' : '#666',
                    textAlign: 'center',
                    fontSize: '0.9rem',
                    maxWidth: 300,
                  }}
                >
                  {t('pages.teams.emptyChat')}
                </Typography>
              </Box>
            </Box>
          ) : (
            <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
              <TeamChat
                teamName={teamName}
                messages={messages}
                isTyping={isTyping}
                onSendMessage={handleSendMessage}
                onTabChange={setActiveTab}
                resultUrl="https://www.figma.com/embed?embed_host=share&url=https%3A%2F%2Fwww.figma.com%2Ffile%2FLKQ4FJ4bTnCSjedbRpk931%2FMaterial-Design-Component-Library%3Fnode-id%3D0%253A1"
              />
            </Box>
          )}

          {/* Message input - show in Conversation tab and when starting a new chat */}
          {(activeTab === 0 || messages.length === 0) && (
            <Box
              sx={{
                p: 2,
                borderTop: '1px solid',
                borderColor: 'divider',
                display: 'flex',
                alignItems: 'center',
                background: (theme) =>
                  theme.palette.mode === 'dark'
                    ? 'linear-gradient(to top, rgba(30, 30, 30, 0.9) 0%, rgba(30, 30, 30, 0.8) 100%)'
                    : 'white',
                backdropFilter: (theme) => (theme.palette.mode === 'dark' ? 'blur(8px)' : 'none'),
                position: 'relative',
                zIndex: 5,
              }}
            >
              <Box sx={{ position: 'relative', width: '100%' }}>
                <TextField
                  fullWidth
                  placeholder={isTyping ? '' : 'Message'}
                  variant="outlined"
                  size="medium"
                  value={isTyping ? '' : inputValue}
                  onChange={(e) => !isTyping && setInputValue(e.target.value)}
                  onKeyDown={(e) => {
                    if (!isTyping && e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      if (inputValue.trim()) {
                        handleSendMessage(inputValue);
                        setInputValue('');
                      }
                    }
                  }}
                  InputProps={{
                    startAdornment: isTyping ? (
                      <Box sx={{ display: 'flex', alignItems: 'center', ml: 1 }}>
                        <Iconify
                          icon="material-symbols:stars-2-rounded"
                          width="24"
                          height="24"
                          sx={{
                            color: (theme) =>
                              theme.palette.mode === 'dark' ? '#FF6B35' : '#FF6B35',
                            mr: 1,
                          }}
                        />
                        <Typography
                          variant="body2"
                          sx={{
                            color: (theme) =>
                              theme.palette.mode === 'dark'
                                ? 'rgba(255, 255, 255, 0.7)'
                                : 'rgba(0, 0, 0, 0.6)',
                          }}
                        >
                          {t('common.agentThinking')}
                        </Typography>
                      </Box>
                    ) : null,
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 4,
                      bgcolor: (theme) =>
                        theme.palette.mode === 'dark'
                          ? alpha(theme.palette.common.white, 0.05)
                          : '#F5F5F5',
                      pr: 5, // Add padding for the button
                      '& fieldset': {
                        borderColor: (theme) =>
                          theme.palette.mode === 'dark'
                            ? alpha(theme.palette.common.white, 0.2)
                            : 'rgba(0, 0, 0, 0.23)',
                      },
                      '&:hover fieldset': {
                        borderColor: (theme) =>
                          theme.palette.mode === 'dark'
                            ? alpha(theme.palette.common.white, 0.3)
                            : 'rgba(0, 0, 0, 0.23)',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: (theme) => theme.palette.primary.main,
                      },
                      '& input': {
                        color: (theme) =>
                          theme.palette.mode === 'dark' ? theme.palette.common.white : 'inherit',
                      },
                      '& input::placeholder': {
                        color: (theme) =>
                          theme.palette.mode === 'dark'
                            ? alpha(theme.palette.common.white, 0.5)
                            : 'inherit',
                        opacity: 1,
                      },
                    },
                  }}
                />
                <IconButton
                  onClick={() => {
                    if (!isTyping && inputValue.trim()) {
                      handleSendMessage(inputValue);
                      setInputValue('');
                    }
                  }}
                  disabled={isTyping}
                  sx={{
                    position: 'absolute',
                    right: 8,
                    top: '50%',
                    transform: 'translateY(-50%)',
                    bgcolor: (theme) =>
                      isTyping
                        ? theme.palette.mode === 'dark'
                          ? 'rgba(255, 107, 53, 0.3)'
                          : '#FFD0BA'
                        : theme.palette.mode === 'dark'
                          ? alpha(theme.palette.primary.main, 0.9)
                          : '#FF6B35',
                    color: 'white',
                    '&:hover': {
                      bgcolor: (theme) =>
                        isTyping
                          ? theme.palette.mode === 'dark'
                            ? 'rgba(255, 107, 53, 0.3)'
                            : '#FFD0BA'
                          : theme.palette.mode === 'dark'
                            ? theme.palette.primary.main
                            : '#E55A2A',
                    },
                    width: 32,
                    height: 32,
                    boxShadow: (theme) =>
                      theme.palette.mode === 'dark' && !isTyping
                        ? '0 0 8px rgba(255, 107, 53, 0.5)'
                        : 'none',
                    opacity: isTyping ? 0.7 : 1,
                  }}
                >
                  <Iconify icon="eva:arrow-forward-fill" width={16} height={16} />
                </IconButton>
              </Box>
            </Box>
          )}
        </Box>
      </Box>

      {/* Team Members Dialog */}
      <TeamMembersDialog
        open={membersDialogOpen}
        onClose={() => setMembersDialogOpen(false)}
        teamName={teamName}
        members={MOCK_TEAM_MEMBERS.map((member) => ({
          id: member.id,
          name: member.name,
          avatar: member.avatar,
          role: 'Social Media',
        }))}
      />
    </>
  );
}
