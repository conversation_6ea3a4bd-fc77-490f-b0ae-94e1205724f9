import type { BoxProps } from '@mui/material/Box';

import Box from '@mui/material/Box';

import { layoutClasses } from '../classes';

// ----------------------------------------------------------------------

export type MainProps = BoxProps & {
  layoutQuery: string;
};

export function Main({ children, layoutQuery, sx, ...other }: MainProps) {
  return (
    <Box
      component="main"
      className={layoutClasses.main}
      sx={{
        display: 'flex',
        flex: '1 1 auto',
        border: '2px solid red',
        pt: 'var(--layout-dashboard-content-pt)',
        pb: 'var(--layout-dashboard-content-pb)',
        px: 'var(--layout-dashboard-content-px)',
        ...sx,
      }}
      {...other}
    >
      {children}
    </Box>
  );
}
