import 'src/global.css';

// ----------------------------------------------------------------------

import { Router } from 'src/routes/sections';

import { useScrollToTop } from 'src/hooks/use-scroll-to-top';

import { ThemeProvider } from 'src/theme/theme-provider';

import { ProgressBar } from 'src/components/progress-bar';
import { MotionLazy } from 'src/components/animate/motion-lazy';
import { SettingsDrawer, defaultSettings, SettingsProvider } from 'src/components/settings';
import { SnackbarProvider } from 'src/components/snackbar';

import { AuthProvider } from 'src/auth/context/jwt';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { DeveloperModeProvider } from 'src/contexts/developer-mode-context';

// ----------------------------------------------------------------------

export default function App() {
  useScrollToTop();
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
        retry: false,
      },
    },
  });
  return (
    <AuthProvider>
      <QueryClientProvider client={queryClient}>
        <SettingsProvider settings={defaultSettings}>
          <DeveloperModeProvider>
            <ThemeProvider>
              <SnackbarProvider>
                <MotionLazy>
                  <ProgressBar />
                  <SettingsDrawer />
                  <Router />
                </MotionLazy>
              </SnackbarProvider>
            </ThemeProvider>
          </DeveloperModeProvider>
        </SettingsProvider>
      </QueryClientProvider>
    </AuthProvider>
  );
}
