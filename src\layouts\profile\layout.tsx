import { useEffect, useState, useCallback } from 'react';
import { Box, Stack, Drawer, useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { usePathname } from 'src/routes/hooks';
import { useResponsive } from 'src/hooks/use-responsive';

// import { useMockedUser } from 'src/auth/hooks';
import { NavSectionVertical } from 'src/components/nav-section';
import { Scrollbar } from 'src/components/scrollbar';
import { WorkforcesLogo } from 'src/components/logo';
import { Iconify } from 'src/components/iconify';
import { SignOutButton } from 'src/layouts/components/sign-out-button';

import { ProfileHeader } from './header';

// Import NAV constant
const NAV = {
  W_VERTICAL: 280,
};

// ----------------------------------------------------------------------

const ProfileNavItems = () => {
  const { t } = useTranslation();

  return [
    {
      subheader: t('navigation.profile.title'),
      items: [
        {
          title: t('navigation.profile.myProfile'),
          path: '/dashboard/profile',
          icon: <Iconify icon="mdi:account-outline" />,
        },
        {
          title: t('navigation.profile.knowledgeBase'),
          path: '/dashboard/profile/knowledge-base',
          icon: <Iconify icon="mdi:book-open-outline" />,
        },
        {
          title: t('navigation.profile.settings'),
          path: '/dashboard/profile/settings',
          icon: <Iconify icon="mdi:cog-outline" />,
        },
      ],
    },
  ];
};

// ----------------------------------------------------------------------

interface ProfileLayoutProps {
  children: React.ReactNode;
}

export function ProfileLayout({ children }: ProfileLayoutProps) {
  const pathname = usePathname();
  const theme = useTheme();
  const lgUp = useResponsive('up', 'lg');

  const [openNav, setOpenNav] = useState(true);

  useEffect(() => {
    if (pathname) {
      window.scrollTo(0, 0);
    }
  }, [pathname]);

  const handleOpenNav = useCallback(() => {
    setOpenNav(true);
  }, []);

  const handleCloseNav = useCallback(() => {
    setOpenNav(false);
  }, []);

  const renderNavVertical = (
    <Scrollbar
      sx={{
        height: 1,
        '& .simplebar-content': {
          height: 1,
          display: 'flex',
          flexDirection: 'column',
        },
      }}
    >
      <Stack
        spacing={3}
        sx={{
          pt: { xs: 3, md: 4 },
          pb: 2,
          px: { xs: 2, md: 2.5 },
          flexShrink: 0,
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'flex-start' }} />
      </Stack>

      <Box sx={{ mt: { xs: 3, md: 4, lg: 5 } }}>
        <NavSectionVertical
          data={ProfileNavItems()}
          sx={{
            px: { xs: 1.5, md: 2 },
            '& .MuiListItemButton-root': {
              borderRadius: 1,
              mb: { xs: 0.5, md: 1 },
              py: { xs: 1, md: 1.5 },
              '&.active': {
                bgcolor: (theme) => theme.palette.action.selected,
                '&:hover': {
                  bgcolor: (theme) => theme.palette.action.hover,
                },
              },
            },
            // Improve typography for tablet devices
            '& .MuiTypography-root': {
              fontSize: { xs: '0.875rem', md: '0.9375rem', lg: '1rem' },
            },
            // Adjust icon size for better tablet display
            '& .MuiListItemIcon-root': {
              minWidth: { xs: 36, md: 40, lg: 44 },
              '& svg': {
                fontSize: { xs: 20, md: 22, lg: 24 },
              },
            },
          }}
        />
      </Box>

      <Box sx={{ flexGrow: 1 }} />

      <Box sx={{ p: { xs: 2, md: 2.5 }, mt: { xs: 1, md: 2 } }}>
        <SignOutButton
          fullWidth
          sx={{
            py: { xs: 0.75, md: 1 },
            fontSize: { xs: '0.8125rem', md: '0.875rem', lg: '0.9375rem' },
          }}
        />
      </Box>
    </Scrollbar>
  );

  const renderContent = (
    <>
      <ProfileHeader onOpenNav={handleOpenNav} />

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          py: { xs: 2, sm: 2.5, md: 3, lg: 4 },
          px: { xs: 2, sm: 2.5, md: 3, lg: 4 },
          mt: { xs: 7, md: 8 }, // Add margin top to account for the fixed header
          transition: theme.transitions.create(['padding'], {
            duration: theme.transitions.duration.standard,
          }),
        }}
      >
        {children}
      </Box>
    </>
  );

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: { xs: 'column', md: 'row' },
        bgcolor: (theme) => theme.palette.background.default,
        overflow: 'hidden', // Prevent content overflow on smaller screens
      }}
    >
      {/* Sidebar for large screens */}
      {lgUp && (
        <Box
          component="nav"
          sx={{
            flexShrink: 0,
            width: NAV.W_VERTICAL,
            borderRight: (theme) => `dashed 1px ${theme.palette.divider}`,
          }}
        >
          {renderNavVertical}
        </Box>
      )}

      {/* Drawer for small and tablet screens */}
      {!lgUp && (
        <Drawer
          open={openNav}
          onClose={handleCloseNav}
          PaperProps={{
            sx: {
              width: { xs: '85%', sm: NAV.W_VERTICAL },
              borderRadius: { xs: 0, sm: '0 8px 8px 0' },
              boxShadow: (theme) => theme.shadows[8],
            },
          }}
        >
          {renderNavVertical}
        </Drawer>
      )}

      {/* Main content */}
      <Box
        sx={{
          flexGrow: 1,
          width: { md: `calc(100% - ${NAV.W_VERTICAL})` },
          transition: theme.transitions.create(['width', 'margin'], {
            duration: theme.transitions.duration.standard,
          }),
        }}
      >
        {renderContent}
      </Box>
    </Box>
  );
}
