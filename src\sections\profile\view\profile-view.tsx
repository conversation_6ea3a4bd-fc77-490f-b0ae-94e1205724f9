import { Card } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';
import { paths } from 'src/routes/paths';
import MyProfileTab from '../tabs/my-profile-tab';

// ----------------------------------------------------------------------

export function ProfileView() {
  const { t } = useTranslation();

  return (
    <>
      <CustomBreadcrumbs
        heading={t('pages.profile.title')}
        links={[
          { name: t('pages.profile.breadcrumbs.dashboard'), href: paths.dashboard.teams.root },
          { name: t('pages.profile.breadcrumbs.profile'), href: paths.dashboard.profile.root },
          { name: t('pages.profile.breadcrumbs.myProfile') },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card sx={{ p: 3 }}>
        <MyProfileTab />
      </Card>
    </>
  );
}
