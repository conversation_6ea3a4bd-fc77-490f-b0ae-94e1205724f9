import LoadingButton from '@mui/lab/LoadingButton';
import { SxProps } from '@mui/material';
import { ButtonProps } from '@mui/material/Button';
import React from 'react';
import { RouterLink } from 'src/routes/components';

export interface IAppButtonProps extends ButtonProps {
  label: string;
  isShow?: boolean;
  isLoading?: boolean;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  type?: 'button' | 'submit';
  href?: string;
  variant?: 'contained' | 'outlined' | 'text' | 'soft';
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning' | 'inherit';
  sx?: SxProps;
  fullWidth?: boolean;

  target?: '_blank' | '_self' | '_parent' | '_top';
}

export const AppButton: React.FC<IAppButtonProps> = (props) => {
  const {
    label,
    isShow = true,
    isLoading = false,
    onClick,
    href,
    type = 'button',
    variant = 'contained',
    color = 'primary',
    fullWidth = true,
    sx,
    target,
    ...other
  } = props;

  return (
    <>
      {isShow ? (
        <LoadingButton
          loading={isLoading}
          onClick={onClick}
          type={type}
          color={color}
          variant={variant}
          fullWidth={fullWidth}
          LinkComponent={href ? (props) => <RouterLink {...props} target={target} /> : undefined}
          href={href}
          sx={{
            borderRadius: 0.9,
            textTransform: 'capitalize',
            ...sx,
          }}
          {...other}
        >
          {label}
        </LoadingButton>
      ) : (
        <></>
      )}
    </>
  );
};

export default AppButton;
