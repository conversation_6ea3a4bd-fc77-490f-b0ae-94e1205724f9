import { useState, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useDeveloperMode } from 'src/contexts/developer-mode-context';
import { TeamMember } from '../view/use-teams-view';

// Frequency options
export const FREQUENCY_OPTIONS = [
  { value: 'daily', label: 'Daily' },
  { value: 'weekly', label: 'Weekly' },
  { value: 'biweekly', label: 'Bi-weekly' },
  { value: 'monthly', label: 'Monthly' },
  { value: 'quarterly', label: 'Quarterly' },
];

// Form validation schema
const teamTemplateSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  flowControl: z.enum(['auto', 'manual']),
  instructions: z.string().optional(),
  selectedAgents: z.array(z.string()).optional(),
  frequency: z.string().optional(),
  startDate: z.date().optional(),
});

// Form values type
export type TeamTemplateFormValues = z.infer<typeof teamTemplateSchema>;

// Team type
export interface Team {
  id: string;
  name: string;
  description: string;
  type: string;
  members: TeamMember[];
  createdAt: Date;
  userCount?: number;
}

// Tool field type
export interface ToolField {
  id: string;
  name: string;
  label: string;
  type: 'text' | 'switch' | 'select';
  required: boolean;
  options?: { value: string; label: string }[];
}

// Tool configuration type
export interface ToolConfig {
  id: string;
  name: string;
  icon: string;
  fields: ToolField[];
}

// Mock agents data for mentions
export interface Agent {
  id: string;
  name: string;
  avatar: string;
}

export const MOCK_AGENTS: Agent[] = [
  {
    id: 'agent1',
    name: 'John Doe',
    avatar: '/assets/images/avatars/avatar_1.jpg',
  },
  {
    id: 'agent2',
    name: 'Jane Smith',
    avatar: '/assets/images/avatars/avatar_2.jpg',
  },
  {
    id: 'agent3',
    name: 'Robert Johnson',
    avatar: '/assets/images/avatars/avatar_3.jpg',
  },
  {
    id: 'agent4',
    name: 'Emily Davis',
    avatar: '/assets/images/avatars/avatar_4.jpg',
  },
  {
    id: 'agent5',
    name: 'Michael Wilson',
    avatar: '/assets/images/avatars/avatar_5.jpg',
  },
];

// Mock tool configurations that would come from an API
export const MOCK_TOOL_CONFIGS: ToolConfig[] = [
  {
    id: 'gmail',
    name: 'Gmail',
    icon: 'logos:google-gmail',
    fields: [
      { id: 'email', name: 'gmail.email', label: 'Email Address', type: 'text', required: true },
      { id: 'apiKey', name: 'gmail.apiKey', label: 'API Key', type: 'text', required: true },
      {
        id: 'notifications',
        name: 'gmail.notifications',
        label: 'Enable Notifications',
        type: 'switch',
        required: false,
      },
    ],
  },
  {
    id: 'facebook',
    name: 'Facebook',
    icon: 'logos:facebook',
    fields: [
      { id: 'pageId', name: 'facebook.pageId', label: 'Page ID', type: 'text', required: true },
      {
        id: 'accessToken',
        name: 'facebook.accessToken',
        label: 'Access Token',
        type: 'text',
        required: true,
      },
      {
        id: 'autoPost',
        name: 'facebook.autoPost',
        label: 'Auto Post Updates',
        type: 'switch',
        required: false,
      },
    ],
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    icon: 'logos:linkedin-icon',
    fields: [
      {
        id: 'companyId',
        name: 'linkedin.companyId',
        label: 'Company ID',
        type: 'text',
        required: true,
      },
      {
        id: 'clientId',
        name: 'linkedin.clientId',
        label: 'Client ID',
        type: 'text',
        required: true,
      },
      {
        id: 'clientSecret',
        name: 'linkedin.clientSecret',
        label: 'Client Secret',
        type: 'text',
        required: true,
      },
    ],
  },
];

// Flow control options
export const FLOW_CONTROL_OPTIONS = [
  { value: 'auto', label: 'Automatic' },
  { value: 'manual', label: 'Manual' },
];

// Steps definition will be created dynamically in the hook to use translations

interface UseTeamTemplateFormProps {
  team: Team | null;
  onSubmit: (data: TeamTemplateFormValues) => void;
}

export function useTeamTemplateForm({ team, onSubmit }: UseTeamTemplateFormProps) {
  const theme = useTheme();
  const { t } = useTranslation();
  const { developerMode } = useDeveloperMode();

  // State for the active step
  const [activeStep, setActiveStep] = useState(0);

  // State for tools
  const [tools, setTools] = useState<ToolConfig[]>([]);
  const [isLoadingTools, setIsLoadingTools] = useState(false);

  // Create steps with translations
  const TEAM_TEMPLATE_FORM_STEPS = [
    {
      label: t('pages.teams.stepper.teamInfo.label'),
      icon: 'mdi:account-group',
      description: t('pages.teams.stepper.teamInfo.description'),
    },
    {
      label: t('pages.teams.stepper.configuration.label'),
      icon: 'mdi:cog-outline',
      description: t('pages.teams.stepper.configuration.description'),
    },
    {
      label: t('pages.teams.stepper.instructions.label'),
      icon: 'mdi:clipboard-text-outline',
      description: t('pages.teams.stepper.instructions.description'),
    },
    {
      label: t('pages.teams.stepper.frequency.label'),
      icon: 'mdi:calendar-clock',
      description: t('pages.teams.stepper.frequency.description'),
    },
  ];

  // Load tools immediately without delay
  useEffect(() => {
    try {
      // Load mock tools immediately without delay
      setTools(MOCK_TOOL_CONFIGS);
    } catch (error) {
      console.error('Error loading tools:', error);
    } finally {
      // Always set loading to false immediately
      setIsLoadingTools(false);
    }
  }, []);

  // Initialize form with default values or team data
  const methods = useForm<TeamTemplateFormValues>({
    mode: 'onChange',
    resolver: zodResolver(teamTemplateSchema),
    defaultValues: team
      ? {
          name: `${team.name} - Copy`,
          description: team.description,
          flowControl: 'auto',
          instructions: '',
          selectedAgents: [],
          frequency: 'weekly',
          startDate: new Date(),
        }
      : {
          name: '',
          description: '',
          flowControl: 'auto',
          instructions: '',
          selectedAgents: [],
          frequency: 'weekly',
          startDate: new Date(),
        },
  });

  const {
    handleSubmit,
    trigger,
    formState: { isSubmitting },
  } = methods;

  // Handle next step
  const handleNext = async () => {
    // Use the filtered steps based on developer mode
    const steps = filteredSteps;
    console.log(`Current step: ${activeStep}, Total steps: ${steps.length}`);

    // Check if we're already at the last step
    if (activeStep >= steps.length - 1) {
      console.log('Already at the last step, not proceeding');
      return;
    }

    console.log(`Attempting to move from step ${activeStep} to step ${activeStep + 1}`);

    // Force step advancement for step 2 (Instructions) to step 3 (Frequency)
    // Only apply this logic if developer mode is enabled and we have more than 2 steps
    if (developerMode && activeStep === 2) {
      console.log('On Instructions step, forcing advancement to Frequency step');
      setActiveStep(3);
      return;
    }

    // Validate fields in the current step based on which step we're on
    let fieldsToValidate: string[] = [];

    // Define fields to validate for each step
    if (activeStep === 0) {
      fieldsToValidate = ['name', 'description', 'flowControl'];
    } else if (activeStep === 1) {
      // For the configuration step, we don't have required fields to validate
      // Just proceed to the next step
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      return;
    } else if (activeStep === 2) {
      fieldsToValidate = ['instructions'];
    } else if (activeStep === 3) {
      fieldsToValidate = ['frequency', 'startDate'];
    }

    const isStepValid = await trigger(fieldsToValidate as any);

    if (isStepValid) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  // Handle back step
  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  // Handle form submission
  const onFormSubmit = async (data: TeamTemplateFormValues) => {
    console.log('Form submission triggered, current step:', activeStep);

    // Validate all fields before submission
    const isValid = await trigger();

    if (isValid) {
      console.log('Form is valid, submitting data:', data);
      // Call the onSubmit callback provided by the parent component
      onSubmit(data);
    } else {
      console.log('Form validation failed');
    }
  };

  // Function to reset form state completely
  const resetForm = () => {
    setActiveStep(0);
    methods.reset();

    // Clear any form-related data from session storage
    try {
      sessionStorage.removeItem('formState');
      sessionStorage.removeItem('activeStep');
      // Clear any other form-related data that might be stored
    } catch (error) {
      console.error('Error clearing session storage:', error);
    }
  };

  // Create a filtered version of steps based on developer mode
  const filteredSteps = useMemo(() => {
    if (developerMode) {
      // If developer mode is enabled, return all steps
      return TEAM_TEMPLATE_FORM_STEPS;
    }
    // If developer mode is disabled, return only the first 2 steps
    return TEAM_TEMPLATE_FORM_STEPS.slice(0, 2);
  }, [developerMode]);

  return {
    theme,
    activeStep,
    methods,
    isSubmitting,
    handleNext,
    handleBack,
    onFormSubmit,
    handleSubmit,
    resetForm,
    FLOW_CONTROL_OPTIONS,
    TEAM_TEMPLATE_FORM_STEPS: filteredSteps,
    tools,
    isLoadingTools,
  };
}
