import { useNavigate } from 'react-router-dom';
import {
  <PERSON>rid,
  Stack,
  Avatar,
  Typography,
  AvatarGroup,
  Box,
  Button,
  Container,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import { paths } from 'src/routes/paths';
import { Label } from 'src/components/label';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import { AppContainerFixed } from 'src/components/common/app-container-fixed';
import { useTeamsView, TeamData, TeamMember } from './use-teams-view';
import { SearchResultsView } from './search-results-view';
import { StyledTeamCard } from '../components/styled-team-card';

// ----------------------------------------------------------------------

// Mock data for team members
const MOCK_MEMBERS: TeamMember[] = [
  {
    id: '1',
    name: '<PERSON>',
    avatarUrl: '/assets/images/avatar/avatar_1.jpg',
    role: 'Team Lead',
  },
  {
    id: '2',
    name: 'Jane Smith',
    avatarUrl: '/assets/images/avatar/avatar_2.jpg',
    role: 'Developer',
  },
  {
    id: '3',
    name: 'Michael Johnson',
    avatarUrl: '/assets/images/avatar/avatar_3.jpg',
    role: 'Designer',
  },
  {
    id: '4',
    name: 'Emily Williams',
    avatarUrl: '/assets/images/avatar/avatar_4.jpg',
    role: 'Marketing',
  },
  {
    id: '5',
    name: 'David Brown',
    avatarUrl: '/assets/images/avatar/avatar_5.jpg',
    role: 'Support',
  },
];

// Mock data for teams
export const MOCK_TEAMS: TeamData[] = [
  {
    id: '1',
    name: 'AI Team',
    description:
      'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze engagement, and optimize content strategies to maximize reach and impact.',
    type: 'Clone',
    members: MOCK_MEMBERS.slice(0, 3),
    createdAt: new Date('2023-01-15'),
    userCount: 50,
    category: 'Social Media',
  },
  {
    id: '2',
    name: 'AI Team',
    description:
      'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze engagement, and optimize content strategies to maximize reach and impact.',
    type: 'Clone',
    members: MOCK_MEMBERS.slice(1, 4),
    createdAt: new Date('2023-02-20'),
    userCount: 50,
    category: 'Research',
  },
  {
    id: '3',
    name: 'AI Team',
    description:
      'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze engagement, and optimize content strategies to maximize reach and impact.',
    type: 'Clone',
    members: MOCK_MEMBERS.slice(2, 5),
    createdAt: new Date('2023-03-10'),
    userCount: 50,
    category: 'Marketing',
  },
  {
    id: '4',
    name: 'AI Team',
    description:
      'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze engagement, and optimize content strategies to maximize reach and impact.',
    type: 'Clone',
    members: MOCK_MEMBERS.slice(0, 5),
    createdAt: new Date('2023-04-05'),
    userCount: 50,
    category: 'Sales',
  },
  {
    id: '5',
    name: 'AI Team',
    description:
      'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze engagement, and optimize content strategies to maximize reach and impact.',
    type: 'Clone',
    members: MOCK_MEMBERS.slice(1, 4),
    createdAt: new Date('2023-05-12'),
    userCount: 50,
    category: 'Social Media',
  },
  {
    id: '6',
    name: 'AI Team',
    description:
      'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze engagement, and optimize content strategies to maximize reach and impact.',
    type: 'Clone',
    members: MOCK_MEMBERS.slice(2, 5),
    createdAt: new Date('2023-06-18'),
    userCount: 50,
    category: 'Marketing',
  },
];

// Category descriptions
const CATEGORY_INFO = {
  'Social Media': {
    title: 'Social Media',
    description:
      'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze engagement, and optimize content strategies to maximize reach and impact.',
    color: '#1DA1F2', // Twitter blue
    icon: 'eva:message-circle-fill',
  },
  Research: {
    title: 'Research',
    description:
      'Accelerate your research with AI-powered data analysis. Discover insights, identify patterns, and generate comprehensive reports to drive innovation and decision-making.',
    color: '#9C27B0', // Purple
    icon: 'eva:search-fill',
  },
  Marketing: {
    title: 'Marketing',
    description:
      'Transform your marketing strategy with AI-driven campaign optimization. Target the right audience, create compelling content, and measure performance to maximize ROI.',
    color: '#FF9800', // Orange
    icon: 'eva:pie-chart-fill',
  },
  Sales: {
    title: 'Sales',
    description:
      'Supercharge your sales process with AI-powered lead generation and qualification. Identify high-value prospects, personalize outreach, and close deals faster.',
    color: '#4CAF50', // Green
    icon: 'eva:shopping-cart-fill',
  },
};

// Get color based on team category
const getTeamCategoryColor = (category: string = '') => {
  return CATEGORY_INFO[category as keyof typeof CATEGORY_INFO]?.color || '#1976D2'; // Default blue
};

// Get icon based on team category
const getTeamCategoryIcon = (category: string = '') => {
  return CATEGORY_INFO[category as keyof typeof CATEGORY_INFO]?.icon || 'eva:people-fill';
};

export const TeamsView = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Use the custom hook to manage teams
  const {
    teams,
    selectedCategory,
    handleSelectCategory,
    recentlyUsedTeams,
    addToRecentlyUsed,
    searchQuery,
    isSearching,
    filteredMembers,
  } = useTeamsView(MOCK_TEAMS);

  // Handle using a team template
  const handleUseTemplate = (teamId: string) => {
    const team = teams.find((t) => t.id === teamId);
    if (team) {
      console.log(`Using template for team: ${team.name}`);
      // Add to recently used teams
      addToRecentlyUsed(teamId);
      // Navigate to the use template page with the team ID
      navigate(paths.dashboard.teams.useTemplate.replace(':id', teamId));
    }
  };

  return (
    <Container maxWidth={false} sx={{ py: 3, px: 3 }}>
      {/* Header */}
      <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          {isSearching
            ? t('pages.teams.searchResults')
            : selectedCategory
              ? CATEGORY_INFO[selectedCategory as keyof typeof CATEGORY_INFO]?.title
              : t('pages.teams.title')}
        </Typography>
        <Button
          variant="contained"
          startIcon={<Iconify icon="eva:plus-fill" />}
          sx={{
            bgcolor: 'primary.main',
            '&:hover': {
              bgcolor: 'primary.dark',
            },
          }}
        >
          {t('pages.teams.createTeam')}
        </Button>
      </Stack>
      {/* Search Results */}
      {isSearching && (
        <SearchResultsView
          searchQuery={searchQuery}
          teams={teams}
          members={filteredMembers}
          onUseTemplate={handleUseTemplate}
        />
      )}

      {!isSearching && (
        <>
          {/* Category description */}
          {selectedCategory && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="body1" color="text.secondary">
                {CATEGORY_INFO[selectedCategory as keyof typeof CATEGORY_INFO]?.description}
              </Typography>
            </Box>
          )}

          {/* Recently Used Teams Section */}
          {recentlyUsedTeams.length > 0 && (
            <Box sx={{ mb: 5 }}>
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                sx={{ mb: 3 }}
              >
                <Typography variant="h5">{t('pages.teams.recentlyUsed')}</Typography>
                <Box
                  sx={{
                    display: 'flex',
                    gap: '2px',
                    cursor: 'pointer',
                    color: 'primary.main',
                    alignItems: 'center',
                    '&:hover': { textDecoration: 'underline' },
                  }}
                  onClick={() => navigate(paths.dashboard.teams.recentlyUsed)}
                >
                  <Typography variant="h6" sx={{ fontWeight: 'bold' }} color="primary">
                    {t('buttons.viewAll')}
                  </Typography>
                  <Iconify
                    icon="weui:arrow-filled"
                    width="24px"
                    height="24px"
                    color="primary.main"
                    sx={{
                      transform: (theme) => (theme.direction === 'rtl' ? 'rotate(180deg)' : 'none'),
                    }}
                  />
                </Box>
              </Stack>

              <Grid container spacing={3}>
                {recentlyUsedTeams.map((team) => (
                  <Grid item xs={12} sm={12} md={4} key={`recent-${team.id}`}>
                    <StyledTeamCard onClick={() => handleUseTemplate(team.id)}>
                      <Stack spacing={2} sx={{ flexGrow: 1 }}>
                        {/* Team Name and Type */}
                        <Stack
                          direction="row"
                          justifyContent="space-between"
                          alignItems="center"
                          spacing={1}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="h6" noWrap>
                              {team.name}
                            </Typography>
                            <Label
                              variant="soft"
                              color="warning"
                              sx={{ textTransform: 'capitalize', fontWeight: 'bold' }}
                            >
                              {team.type}
                            </Label>
                          </Box>
                          <Stack direction="row" alignItems="center" spacing={0.5}>
                            <Iconify
                              icon="ri:box-3-fill"
                              sx={{ color: 'grayText', width: 24, height: 24 }}
                            />
                            <Typography variant="subtitle2" sx={{ color: 'text.secondary' }}>
                              {team.userCount || 0}
                            </Typography>
                          </Stack>
                        </Stack>
                      </Stack>

                      {/* Card Footer - Avatars and Use Template Button */}
                      <Stack
                        direction="row"
                        justifyContent="space-between"
                        alignItems="center"
                        sx={{ mt: 2, pt: 2 }}
                      >
                        <AvatarGroup
                          max={3}
                          sx={{
                            '& .MuiAvatar-root': {
                              width: 32,
                              height: 32,
                              fontSize: '0.875rem',
                              border: (theme) => `2px solid ${theme.palette.background.paper}`,
                            },
                          }}
                        >
                          {team.members.map((member) => (
                            <Avatar
                              key={member.id}
                              alt={member.name}
                              src={member.avatarUrl}
                              title={member.name}
                            />
                          ))}
                        </AvatarGroup>

                        <AppButton
                          label={t('pages.teams.useTemplate')}
                          fullWidth={false}
                          variant="outlined"
                          size="small"
                          color="primary"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleUseTemplate(team.id);
                          }}
                          sx={{
                            px: 2,
                          }}
                        />
                      </Stack>
                    </StyledTeamCard>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {/* All Teams Section Title */}
          <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
            <Typography variant="h5">{t('pages.teams.title')}</Typography>
          </Stack>

          {/* Category filters */}
          <Box
            sx={{
              mb: 3,
              display: 'flex',
              flexWrap: 'wrap',
              gap: { xs: 1, sm: 1.5, md: 2 },
              width: '100%',
            }}
          >
            {Object.keys(CATEGORY_INFO).map((category) => (
              <Box
                key={category}
                onClick={() => handleSelectCategory(category)}
                sx={{
                  display: 'flex',
                  width: {
                    xs: 'calc(50% - 4px)',
                    sm: 'calc(33.33% - 8px)',
                    md: 'calc(25% - 12px)',
                  },
                  alignItems: 'center',
                  justifyContent: { xs: 'center', sm: 'flex-start' },
                  gap: { xs: 0.5, sm: 0.75, md: 1 },
                  px: { xs: 1, sm: 1.5, md: 2 },
                  py: { xs: 0.75, sm: 1 },
                  borderRadius: 1,
                  bgcolor:
                    selectedCategory === category
                      ? `${getTeamCategoryColor(category)}10`
                      : 'background.paper',
                  border: '1px solid',
                  borderColor:
                    selectedCategory === category ? getTeamCategoryColor(category) : 'divider',
                  boxShadow: (theme) => theme.shadows[1],
                  cursor: 'pointer',
                  transition: (theme) =>
                    theme.transitions.create(['background-color', 'border-color']),
                  '&:hover': {
                    bgcolor:
                      selectedCategory === category
                        ? `${getTeamCategoryColor(category)}20`
                        : 'action.hover',
                  },
                  minHeight: { xs: 40, sm: 44, md: 48 },
                }}
              >
                <Iconify
                  icon={getTeamCategoryIcon(category)}
                  sx={{
                    color: getTeamCategoryColor(category),
                    width: { xs: 16, sm: 18, md: 20 },
                    height: { xs: 16, sm: 18, md: 20 },
                    flexShrink: 0,
                  }}
                />
                <Typography
                  variant="body2"
                  sx={{
                    color:
                      selectedCategory === category
                        ? getTeamCategoryColor(category)
                        : 'text.primary',
                    fontWeight: selectedCategory === category ? 'bold' : 'normal',
                    fontSize: { xs: '0.75rem', sm: '0.8125rem', md: '0.875rem' },
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}
                >
                  {category}
                </Typography>
              </Box>
            ))}
          </Box>

          <Grid container spacing={3}>
            {teams.map((team) => (
              <Grid item xs={12} sm={12} md={4} key={team.id}>
                <StyledTeamCard onClick={() => handleUseTemplate(team.id)}>
                  <Stack spacing={2} sx={{ flexGrow: 1 }}>
                    {/* Team Name and Type */}
                    <Stack
                      direction="row"
                      justifyContent="space-between"
                      alignItems="center"
                      spacing={1}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="h6" noWrap>
                          {team.name}
                        </Typography>
                        <Label
                          variant="soft"
                          color="warning"
                          sx={{ textTransform: 'capitalize', fontWeight: 'bold' }}
                        >
                          {team.type}
                        </Label>
                      </Box>
                      <Stack direction="row" alignItems="center" spacing={0.5}>
                        <Iconify
                          icon="ri:box-3-fill"
                          sx={{ color: 'grayText', width: 24, height: 24 }}
                        />
                        <Typography variant="subtitle2" sx={{ color: 'text.secondary' }}>
                          {team.userCount || 0}
                        </Typography>
                      </Stack>
                    </Stack>

                    {/* Team Description */}
                    <Typography variant="body2" sx={{ color: 'text.secondary', flexGrow: 1 }}>
                      {team.description}
                    </Typography>
                  </Stack>

                  {/* Card Footer - Avatars and Use Template Button */}
                  <Stack
                    direction="row"
                    justifyContent="space-between"
                    alignItems="center"
                    sx={{ mt: 2, pt: 2 }}
                  >
                    <AvatarGroup
                      max={3}
                      sx={{
                        '& .MuiAvatar-root': {
                          width: 32,
                          height: 32,
                          fontSize: '0.875rem',
                          border: (theme) => `2px solid ${theme.palette.background.paper}`,
                        },
                      }}
                    >
                      {team.members.map((member) => (
                        <Avatar
                          key={member.id}
                          alt={member.name}
                          src={member.avatarUrl}
                          title={member.name}
                        />
                      ))}
                    </AvatarGroup>

                    <AppButton
                      label={t('pages.teams.useTemplate')}
                      fullWidth={false}
                      variant="outlined"
                      size="small"
                      color="primary"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleUseTemplate(team.id);
                      }}
                      sx={{
                        px: 2,
                      }}
                    />
                  </Stack>
                </StyledTeamCard>
              </Grid>
            ))}
          </Grid>
        </>
      )}
    </Container>
  );
};
