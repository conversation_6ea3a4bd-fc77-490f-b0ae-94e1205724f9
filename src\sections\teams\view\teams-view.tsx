import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  TextField,
  InputAdornment,
  Chip,
  Avatar,
  IconButton,
  Grid,
} from '@mui/material';
import { Icon } from '@iconify/react';
import { useTheme } from '@mui/material/styles';

const TeamsView = () => {
  const theme = useTheme();
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');

  const categories = ['All', 'Social Media', 'Marketing', 'Sales'];

  const recentlyUsedTeam = {
    name: 'AI Team',
    type: 'Template',
    description:
      'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze eng...',
    platforms: [
      { name: 'Telegram', icon: 'logos:telegram', color: '#0088cc' },
      { name: 'Pinterest', icon: 'logos:pinterest', color: '#bd081c' },
    ],
  };

  const teamTemplates = [
    {
      name: 'AI Team',
      type: 'Template',
      description:
        'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze eng...',
      platforms: [
        { name: 'Telegram', icon: 'logos:telegram', color: '#0088cc' },
        { name: 'Figma', icon: 'logos:figma', color: '#f24e1e' },
        { name: 'Gmail', icon: 'logos:google-gmail', color: '#ea4335' },
        { name: 'Notion', icon: 'logos:notion-icon', color: '#000000' },
        { name: 'GitHub', icon: 'logos:github-icon', color: '#181717' },
      ],
    },
    {
      name: 'AI Team',
      type: 'Template',
      description:
        'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze eng...',
      platforms: [
        { name: 'Telegram', icon: 'logos:telegram', color: '#0088cc' },
        { name: 'Pinterest', icon: 'logos:pinterest', color: '#bd081c' },
      ],
    },
    {
      name: 'AI Team',
      type: 'Template',
      description:
        'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze eng...',
      platforms: [
        { name: 'Atlassian', icon: 'logos:atlassian', color: '#0052cc' },
        { name: 'Gmail', icon: 'logos:google-gmail', color: '#ea4335' },
      ],
    },
    {
      name: 'AI Team',
      type: 'Template',
      description:
        'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze eng...',
      platforms: [
        { name: 'Behance', icon: 'logos:behance', color: '#1769ff' },
        { name: 'Pinterest', icon: 'logos:pinterest', color: '#bd081c' },
        { name: 'Mastercard', icon: 'logos:mastercard', color: '#eb001b' },
        { name: 'Vimeo', icon: 'logos:vimeo-icon', color: '#1ab7ea' },
        { name: 'GitHub', icon: 'logos:github-icon', color: '#181717' },
      ],
    },
    {
      name: 'AI Team',
      type: 'Template',
      description:
        'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze eng...',
      platforms: [
        { name: 'Gmail', icon: 'logos:google-gmail', color: '#ea4335' },
        { name: 'Notion', icon: 'logos:notion-icon', color: '#000000' },
      ],
    },
    {
      name: 'AI Team',
      type: 'Template',
      description:
        'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze eng...',
      platforms: [
        { name: 'Telegram', icon: 'logos:telegram', color: '#0088cc' },
        { name: 'Figma', icon: 'logos:figma', color: '#f24e1e' },
        { name: 'Gmail', icon: 'logos:google-gmail', color: '#ea4335' },
        { name: 'Notion', icon: 'logos:notion-icon', color: '#000000' },
        { name: 'GitHub', icon: 'logos:github-icon', color: '#181717' },
      ],
    },
  ];

  const TeamCard = ({ team, isRecentlyUsed = false }) => (
    <Card
      sx={{
        p: 2,
        height: '100%',
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: 2,
        '&:hover': {
          boxShadow: theme.shadows[4],
        },
      }}
    >
      <CardContent sx={{ p: 0, '&:last-child': { pb: 0 } }}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
          <Box display="flex" alignItems="center" gap={1}>
            <Typography variant="h6" fontWeight={600}>
              {team.name}
            </Typography>
            <Chip
              label={team.type}
              size="small"
              sx={{
                bgcolor: '#f3e8ff',
                color: '#7c3aed',
                fontWeight: 500,
                fontSize: '0.75rem',
              }}
            />
          </Box>
          <IconButton size="small">
            <Icon icon="eva:more-horizontal-fill" />
          </IconButton>
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, lineHeight: 1.4 }}>
          {team.description}
        </Typography>

        <Box display="flex" alignItems="center" gap={0.5} mb={2}>
          {team.platforms.slice(0, 5).map((platform, index) => (
            <Avatar
              key={index}
              sx={{
                width: 24,
                height: 24,
                bgcolor: 'transparent',
              }}
            >
              <Icon icon={platform.icon} width={20} height={20} />
            </Avatar>
          ))}
          {team.platforms.length > 5 && (
            <Avatar
              sx={{
                width: 24,
                height: 24,
                bgcolor: 'grey.800',
                fontSize: '0.75rem',
                color: 'white',
              }}
            >
              +{team.platforms.length - 5}
            </Avatar>
          )}
        </Box>

        <Button
          variant="outlined"
          fullWidth
          sx={{
            borderColor: '#e5e7eb',
            color: '#6b7280',
            '&:hover': {
              borderColor: '#d1d5db',
              bgcolor: '#f9fafb',
            },
          }}
        >
          Use Template
        </Button>
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography variant="h4" fontWeight={700}>
          Teams
        </Typography>
        <Button
          variant="contained"
          startIcon={<Icon icon="eva:plus-fill" />}
          sx={{
            bgcolor: '#7c3aed',
            '&:hover': {
              bgcolor: '#6d28d9',
            },
          }}
        >
          Create Team
        </Button>
      </Box>

      {/* Recently Used Section */}
      <Box mb={4}>
        <Typography variant="h6" fontWeight={600} mb={2}>
          Recently Used
        </Typography>
        <Box maxWidth={300}>
          <TeamCard team={recentlyUsedTeam} isRecentlyUsed />
        </Box>
      </Box>

      {/* Teams Templates Section */}
      <Box sx={{ widht: '100%' }}>
        <Typography variant="h6" fontWeight={600} mb={2}>
          Teams Templates
        </Typography>

        {/* Search Bar */}
        <TextField
          placeholder="Search"
          fullWidth={true}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Icon icon="eva:search-fill" />
              </InputAdornment>
            ),
          }}
          sx={{
            mb: 3,
            '& .MuiOutlinedInput-root': {
              bgcolor: 'background.paper',
            },
          }}
        />

        {/* Category Filters */}
        <Box display="flex" gap={1} mb={3}>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              onClick={() => setSelectedCategory(category)}
              sx={{
                bgcolor: selectedCategory === category ? '#f3e8ff' : 'transparent',
                color: selectedCategory === category ? '#7c3aed' : 'text.secondary',
                border: '1px solid',
                borderColor: selectedCategory === category ? '#7c3aed' : 'divider',
                '&:hover': {
                  bgcolor: selectedCategory === category ? '#f3e8ff' : '#f9fafb',
                },
              }}
            />
          ))}
        </Box>

        {/* Templates Grid */}
        <Grid container spacing={3}>
          {teamTemplates.map((template, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <TeamCard team={template} />
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
};

export default TeamsView;
