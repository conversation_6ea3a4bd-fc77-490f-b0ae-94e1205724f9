import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { RecentlyUsedView } from 'src/sections/teams/view/recently-used-view';

// ----------------------------------------------------------------------

export default function RecentlyUsedPage() {
  const { t } = useTranslation();

  return (
    <>
      <Helmet>
        <title>{t('pages.teams.recentlyUsed')}</title>
      </Helmet>

      <RecentlyUsedView />
    </>
  );
}
