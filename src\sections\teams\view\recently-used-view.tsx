import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Grid, Stack, Avatar, Typography, AvatarGroup, Box, Pagination } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { paths } from 'src/routes/paths';
import { Label } from 'src/components/label';
import { Iconify } from 'src/components/iconify';
import { AppButton, AppContainerFixed } from 'src/components/common';
import { TeamData } from 'src/sections/teams/view/use-teams-view';
import { MOCK_TEAMS } from 'src/sections/teams/view/teams-view';
import { StyledTeamCard } from '../components/styled-team-card';

// ----------------------------------------------------------------------

export function RecentlyUsedView() {
  const navigate = useNavigate();
  const [recentlyUsedTeams, setRecentlyUsedTeams] = useState<TeamData[]>([]);
  const [page, setPage] = useState(1);
  const teamsPerPage = 8;
  const { t } = useTranslation();
  // Get recently used teams from localStorage
  useEffect(() => {
    try {
      const recentlyUsedIds = JSON.parse(localStorage.getItem('recentlyUsedTeams') || '[]');

      // Get the teams that match the IDs in recentlyUsedIds
      const recentTeams = recentlyUsedIds
        .map((id: string) => MOCK_TEAMS.find((team) => team.id === id))
        .filter((team: unknown): team is TeamData => team !== undefined);

      // If we couldn't find any teams, use the first few teams as default
      setRecentlyUsedTeams(recentTeams.length > 0 ? recentTeams : MOCK_TEAMS.slice(0, 6));
    } catch (error) {
      console.error('Error getting recently used teams:', error);
      setRecentlyUsedTeams(MOCK_TEAMS.slice(0, 6));
    }
  }, []);

  // Handle using a team template
  const handleUseTemplate = (teamId: string) => {
    const team = MOCK_TEAMS.find((t) => t.id === teamId);
    if (team) {
      console.log(`Using template for team: ${team.name}`);

      // Update recently used teams in localStorage
      try {
        const recentlyUsedIds = JSON.parse(localStorage.getItem('recentlyUsedTeams') || '[]');
        const filteredIds = recentlyUsedIds.filter((id: string) => id !== teamId);
        const newRecentlyUsedIds = [teamId, ...filteredIds];
        localStorage.setItem('recentlyUsedTeams', JSON.stringify(newRecentlyUsedIds));
      } catch (error) {
        console.error('Error updating recently used teams:', error);
      }

      // Navigate to the use template page
      navigate(paths.dashboard.teams.useTemplate.replace(':id', teamId));
    }
  };

  // Handle page change
  const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
    window.scrollTo(0, 0);
  };

  // Calculate pagination
  const totalPages = Math.ceil(recentlyUsedTeams.length / teamsPerPage);
  const displayedTeams = recentlyUsedTeams.slice((page - 1) * teamsPerPage, page * teamsPerPage);

  return (
    <AppContainerFixed title={t('pages.teams.recentlyUsed')}>
      {/* Teams Grid */}
      <Grid container spacing={3}>
        {displayedTeams.map((team) => (
          <Grid item xs={12} sm={6} md={4} lg={4} key={team.id}>
            <StyledTeamCard onClick={() => handleUseTemplate(team.id)}>
              <Stack spacing={2} sx={{ flexGrow: 1 }}>
                {/* Team Name and Type */}
                <Stack
                  direction="row"
                  justifyContent="space-between"
                  alignItems="center"
                  spacing={1}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="h6" noWrap>
                      {team.name}
                    </Typography>
                    <Label
                      variant="soft"
                      color="warning"
                      sx={{ textTransform: 'capitalize', fontWeight: 'bold' }}
                    >
                      {team.type}
                    </Label>
                  </Box>
                  <Stack direction="row" alignItems="center" spacing={0.5}>
                    <Iconify
                      icon="ri:box-3-fill"
                      sx={{ color: 'grayText', width: 24, height: 24 }}
                    />
                    <Typography variant="subtitle2" sx={{ color: 'text.secondary' }}>
                      {team.userCount || 0}
                    </Typography>
                  </Stack>
                </Stack>

                {/* Team Description */}
                <Typography variant="body2" sx={{ color: 'text.secondary', flexGrow: 1 }}>
                  {team.description}
                </Typography>
              </Stack>

              {/* Card Footer - Avatars and Use Template Button */}
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
                sx={{ mt: 2, pt: 2 }}
              >
                <AvatarGroup
                  max={3}
                  sx={{
                    '& .MuiAvatar-root': {
                      width: 32,
                      height: 32,
                      fontSize: '0.875rem',
                      border: (theme) => `2px solid ${theme.palette.background.paper}`,
                    },
                  }}
                >
                  {team.members.map((member) => (
                    <Avatar
                      key={member.id}
                      alt={member.name}
                      src={member.avatarUrl}
                      title={member.name}
                    />
                  ))}
                </AvatarGroup>

                <AppButton
                  label={t('pages.teams.useTemplate')}
                  fullWidth={false}
                  variant="outlined"
                  size="small"
                  color="primary"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleUseTemplate(team.id);
                  }}
                  sx={{
                    px: 2,
                  }}
                />
              </Stack>
            </StyledTeamCard>
          </Grid>
        ))}
      </Grid>

      {/* Pagination */}
      {totalPages > 1 && (
        <Stack direction="row" justifyContent="center" sx={{ mt: 5 }}>
          <Pagination
            count={totalPages}
            page={page}
            onChange={handlePageChange}
            color="primary"
            showFirstButton
            showLastButton
          />
        </Stack>
      )}
    </AppContainerFixed>
  );
}
