import { useState } from 'react';
import { Box, Grid, Stack, InputAdornment, IconButton } from '@mui/material';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Form } from 'src/components/hook-form/form-provider';
import { Field } from 'src/components/hook-form/fields';
import { AppButton } from 'src/components/common';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

// Form validation schema
const profileSchema = z
  .object({
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
    email: z.string().email('Email must be a valid email address'),
    username: z.string().min(3, 'Username must be at least 3 characters'),
    password: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
      .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
      .regex(/[0-9]/, 'Password must contain at least one number')
      .optional()
      .or(z.literal('')),
    confirmPassword: z.string().optional().or(z.literal('')),
  })
  .refine(
    (data) => {
      // If password is provided, confirmPassword must match
      if (data.password && data.password !== data.confirmPassword) {
        return false;
      }
      return true;
    },
    {
      message: "Passwords don't match",
      path: ['confirmPassword'],
    }
  );

// Form values type
type ProfileFormValues = z.infer<typeof profileSchema>;

// Mock user data
const mockUserData = {
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  username: 'johndoe',
  password: '',
  confirmPassword: '',
};

export default function MyProfileTab() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Initialize form with default values
  const methods = useForm<ProfileFormValues>({
    mode: 'onChange',
    resolver: zodResolver(profileSchema),
    defaultValues: mockUserData,
  });

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  // Handle form submission
  const onSubmit = async (data: ProfileFormValues) => {
    try {
      // In a real app, you would send this data to an API
      console.log('Profile data submitted:', data);

      // Show success message
      alert('Profile updated successfully!');
    } catch (error) {
      console.error('Error updating profile:', error);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    methods.reset(mockUserData);
  };

  return (
    <Form methods={methods} onSubmit={handleSubmit(onSubmit)}>
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Field.Text name="firstName" label="First Name" />
        </Grid>

        <Grid item xs={12} md={6}>
          <Field.Text name="lastName" label="Last Name" />
        </Grid>

        <Grid item xs={12} md={6}>
          <Field.Text name="email" label="Email" />
        </Grid>

        <Grid item xs={12} md={6}>
          <Field.Text name="username" label="Username" />
        </Grid>

        <Grid item xs={12} md={6}>
          <Field.Text
            name="password"
            label="Password"
            type={showPassword ? 'text' : 'password'}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={() => setShowPassword(!showPassword)} edge="end">
                    <Iconify icon={showPassword ? 'eva:eye-fill' : 'eva:eye-off-fill'} />
                  </IconButton>
                </InputAdornment>
              ),
            }}
            helperText="Leave blank to keep current password"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Field.Text
            name="confirmPassword"
            label="Confirm Password"
            type={showConfirmPassword ? 'text' : 'password'}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    edge="end"
                  >
                    <Iconify icon={showConfirmPassword ? 'eva:eye-fill' : 'eva:eye-off-fill'} />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12}>
          <Stack direction="row" justifyContent="flex-end" spacing={2}>
            <AppButton label="Save" type="submit" variant="contained" isLoading={isSubmitting} />
            <AppButton label="Cancel" variant="outlined" color="inherit" onClick={handleCancel} />
          </Stack>
        </Grid>
      </Grid>
    </Form>
  );
}
