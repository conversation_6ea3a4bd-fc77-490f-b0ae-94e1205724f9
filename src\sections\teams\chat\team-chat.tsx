import React, { useRef, useEffect, useState } from 'react';
import { <PERSON>, Stack, Avatar, Typography, Paper, Chip, Tabs, Tab, alpha } from '@mui/material';
import { Message, TeamMember } from './use-team-chat';

// ----------------------------------------------------------------------

interface TeamChatProps {
  teamName: string;
  messages: Message[];
  isTyping: boolean;
  onSendMessage: (message: string) => void;
  onTabChange?: (tabIndex: number) => void;
  resultUrl?: string; // URL to display in the iframe in the Result tab
}

export function TeamChat({
  teamName,
  messages,
  isTyping,
  onSendMessage,
  onTabChange,
  resultUrl = 'https://www.example.com', // Default URL if none provided
}: TeamChatProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [activeTab, setActiveTab] = useState(0);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle tab change
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    // Notify parent component about tab change if callback is provided
    if (onTabChange) {
      onTabChange(newValue);
    }
  };

  // Helper function to highlight mentions in text
  const formatMessageContent = (content: string) => {
    // Split by @ mentions
    const parts = content.split(/(@\w+)/g);

    return parts.map((part, index) => {
      if (part.startsWith('@')) {
        return (
          <Typography
            component="span"
            key={index}
            sx={{
              color: (theme) =>
                theme.palette.mode === 'dark'
                  ? '#d2a8ff' // Lighter purple for dark mode
                  : '#8250df', // Original purple for light mode
              fontWeight: 'bold',
            }}
          >
            {part}
          </Typography>
        );
      }
      return <React.Fragment key={index}>{part}</React.Fragment>;
    });
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Chat header with tabs */}
      <Box
        sx={{
          px: 2,
          background: (theme) =>
            theme.palette.mode === 'dark'
              ? 'linear-gradient(to right, rgba(30, 30, 30, 0.9), rgba(255, 111, 60, 0.05), rgba(30, 30, 30, 0.9))'
              : 'white',
          backdropFilter: (theme) => (theme.palette.mode === 'dark' ? 'blur(5px)' : 'none'),
          position: 'relative',
          zIndex: 5,
          borderBottom: '1px solid',
          borderColor: (theme) =>
            theme.palette.mode === 'dark' ? 'rgba(255, 111, 60, 0.2)' : 'rgba(255, 111, 60, 0.1)',
        }}
      >
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          sx={{
            '& .MuiTabs-indicator': {
              backgroundColor: (theme) =>
                theme.palette.mode === 'dark'
                  ? 'rgba(255, 111, 60, 0.8)'
                  : theme.palette.primary.main,
              height: 3,
            },
            '& .MuiTab-root': {
              textTransform: 'none',
              fontWeight: 'medium',
              fontSize: '0.875rem',
              minWidth: 120,
              color: (theme) =>
                theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
              '&.Mui-selected': {
                color: (theme) =>
                  theme.palette.mode === 'dark' ? 'white' : theme.palette.primary.main,
                fontWeight: 'bold',
              },
            },
          }}
        >
          <Tab label="Conversation" />
          <Tab label="Result" />
        </Tabs>
      </Box>

      {/* Tab content */}
      {activeTab === 0 ? (
        // Conversation Tab Content
        <>
          {/* Chat messages */}
          <Box
            sx={{
              flexGrow: 1,
              overflow: 'auto',
              p: 2,
              background: (theme) =>
                theme.palette.mode === 'dark'
                  ? 'linear-gradient(to bottom, rgba(30, 30, 30, 0.9) 0%, rgba(30, 30, 30, 0.95) 50%, rgba(30, 30, 30, 0.9) 100%)'
                  : 'white',
              backdropFilter: (theme) => (theme.palette.mode === 'dark' ? 'blur(10px)' : 'none'),
              border: '1px solid',
              borderColor: (theme) =>
                theme.palette.mode === 'dark'
                  ? 'rgba(255, 111, 60, 0.2)'
                  : 'rgba(255, 111, 60, 0.1)',
              borderRadius: 2,
              boxShadow: (theme) =>
                theme.palette.mode === 'dark'
                  ? '0 4px 20px rgba(0, 0, 0, 0.2)'
                  : '0 4px 20px rgba(0, 0, 0, 0.05)',
              mt: '33px',
            }}
          >
            {messages.length > 0 ? (
              <Stack spacing={2} sx={{ width: '100%' }}>
                {/* All messages in chronological order */}
                {messages
                  .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
                  .map((message) =>
                    message.role === 'user' ? (
                      <Box
                        key={message.id}
                        sx={{
                          display: 'flex',
                          justifyContent: 'flex-end',
                          mb: 2,
                          width: '100%',
                        }}
                      >
                        {message.isPrompt ? (
                          <Chip
                            label={message.content}
                            variant="soft"
                            sx={{
                              bgcolor: (theme) =>
                                theme.palette.mode === 'dark'
                                  ? alpha(theme.palette.primary.main, 0.2)
                                  : theme.palette.primary.light,
                              color: (theme) =>
                                theme.palette.mode === 'dark'
                                  ? theme.palette.common.white
                                  : 'black',
                              borderRadius: 4,
                              px: 1,
                              py: 0.5,
                              fontSize: '0.875rem',
                              maxWidth: '75%',
                            }}
                          />
                        ) : (
                          <Paper
                            elevation={0}
                            sx={{
                              p: 2,
                              maxWidth: '75%',
                              borderRadius: 2,
                              bgcolor: (theme) =>
                                theme.palette.mode === 'dark'
                                  ? alpha(theme.palette.primary.main, 0.2)
                                  : theme.palette.primary.light,
                              color: (theme) =>
                                theme.palette.mode === 'dark'
                                  ? theme.palette.common.white
                                  : 'black',
                              borderTopRightRadius: 0,
                            }}
                          >
                            <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                              {message.content}
                            </Typography>
                          </Paper>
                        )}
                      </Box>
                    ) : (
                      <Box
                        key={message.id}
                        sx={{
                          mb: 2,
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'flex-start',
                          width: '100%',
                        }}
                      >
                        {message.sender && (
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <Avatar
                              src={message.sender.avatar}
                              alt={message.sender.name}
                              sx={{ width: 32, height: 32, mr: 1 }}
                            />
                            <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                              {message.sender.name}
                              {message.sender.role && `, ${message.sender.role}`}
                            </Typography>
                          </Box>
                        )}

                        <Paper
                          elevation={0}
                          sx={{
                            p: 2,
                            ml: message.sender ? 4 : 0,
                            maxWidth: '90%',
                            borderRadius: 2,
                            bgcolor: (theme) =>
                              theme.palette.mode === 'dark'
                                ? alpha(theme.palette.common.white, 0.05)
                                : '#f5f5f5',
                            color: (theme) =>
                              theme.palette.mode === 'dark' ? theme.palette.common.white : '#333',
                            borderTopLeftRadius: 0,
                          }}
                        >
                          <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                            {formatMessageContent(message.content)}
                          </Typography>
                        </Paper>
                      </Box>
                    )
                  )}

                {/* Typing indicator with avatar and name */}
                {isTyping && (
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'flex-start',
                      width: '100%',
                      mb: 2,
                      borderBottom: (theme) =>
                        `1px dashed ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : '#e0e0e0'}`,
                      pb: 2,
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        width: '100%',
                      }}
                    >
                      <Avatar
                        src="/assets/images/avatars/avatar_2.jpg"
                        alt="Data Analyzer"
                        sx={{ width: 32, height: 32, mr: 1 }}
                      />
                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                          Data Analyzer
                        </Typography>
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            mt: 0.5,
                          }}
                        >
                          <Box
                            component="span"
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              bgcolor: '#FF6B35',
                              mr: 0.5,
                              opacity: 0.3,
                              animation: 'pulse 1.4s infinite',
                              animationDelay: '0s',
                              '@keyframes pulse': {
                                '0%': { opacity: 0.3 },
                                '33%': { opacity: 1 },
                                '66%': { opacity: 0.3 },
                                '100%': { opacity: 0.3 },
                              },
                            }}
                          />
                          <Box
                            component="span"
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              bgcolor: '#FF6B35',
                              mr: 0.5,
                              opacity: 0.3,
                              animation: 'pulse 1.4s infinite',
                              animationDelay: '0.2s',
                              '@keyframes pulse': {
                                '0%': { opacity: 0.3 },
                                '33%': { opacity: 1 },
                                '66%': { opacity: 0.3 },
                                '100%': { opacity: 0.3 },
                              },
                            }}
                          />
                          <Box
                            component="span"
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              bgcolor: '#FF6B35',
                              opacity: 0.3,
                              animation: 'pulse 1.4s infinite',
                              animationDelay: '0.4s',
                              '@keyframes pulse': {
                                '0%': { opacity: 0.3 },
                                '33%': { opacity: 1 },
                                '66%': { opacity: 0.3 },
                                '100%': { opacity: 0.3 },
                              },
                            }}
                          />
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                )}

                <div ref={messagesEndRef} />
              </Stack>
            ) : null}
          </Box>
        </>
      ) : (
        // Result Tab Content - Web View (iframe)
        <Box
          sx={{
            flexGrow: 1,
            display: 'flex',
            flexDirection: 'column',
            width: '100%',
            height: '100%',
            mt: '33px',
            background: (theme) =>
              theme.palette.mode === 'dark'
                ? 'linear-gradient(to bottom, rgba(30, 30, 30, 0.9) 0%, rgba(30, 30, 30, 0.95) 50%, rgba(30, 30, 30, 0.9) 100%)'
                : 'white',
            backdropFilter: (theme) => (theme.palette.mode === 'dark' ? 'blur(10px)' : 'none'),
            border: '1px solid',
            borderColor: (theme) =>
              theme.palette.mode === 'dark' ? 'rgba(255, 111, 60, 0.2)' : 'rgba(255, 111, 60, 0.1)',
            borderRadius: 2,
            boxShadow: (theme) =>
              theme.palette.mode === 'dark'
                ? '0 4px 20px rgba(0, 0, 0, 0.2)'
                : '0 4px 20px rgba(0, 0, 0, 0.05)',
            overflow: 'hidden',
          }}
        >
          <Box
            component="iframe"
            src={resultUrl}
            title="Web View"
            sx={{
              width: '100%',
              height: '100%',
              border: 'none',
              flexGrow: 1,
              minHeight: 'calc(100vh - 200px)',
            }}
            sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
            loading="lazy"
          />
        </Box>
      )}
    </Box>
  );
}
