import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

import { en } from './locales/en';
import { ar } from './locales/ar';

const lng = localStorage.getItem('i18nextLng') || 'en';

// Resources
const resources = {
  en: { translation: en },
  ar: { translation: ar },
};

// Initialize i18n with Language Detector
i18n
  .use(LanguageDetector) // Add the language detector
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    lng,
    interpolation: {
      escapeValue: false, // React already handles XSS
    },

    detection: {
      order: ['localStorage', 'cookie', 'navigator', 'htmlTag', 'path', 'subdomain'],
      caches: ['localStorage', 'cookie'], // Save language choice in localStorage and cookies
    },
  });

export default i18n;
