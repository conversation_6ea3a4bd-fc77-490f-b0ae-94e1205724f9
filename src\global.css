/** **************************************
* Fonts: app
*************************************** */
@import '@fontsource/public-sans/400.css';
@import '@fontsource/public-sans/500.css';
@import '@fontsource/public-sans/600.css';
@import '@fontsource/public-sans/700.css';
@import '@fontsource/public-sans/800.css';

@import '@fontsource/barlow/400.css';
@import '@fontsource/barlow/500.css';
@import '@fontsource/barlow/600.css';
@import '@fontsource/barlow/700.css';
@import '@fontsource/barlow/800.css';

/** **************************************
* Fonts: options
*************************************** */
@import '@fontsource/dm-sans/400.css';
@import '@fontsource/dm-sans/500.css';
@import '@fontsource/dm-sans/600.css';
@import '@fontsource/dm-sans/700.css';
@import '@fontsource/dm-sans/800.css';

@import '@fontsource/inter/400.css';
@import '@fontsource/inter/500.css';
@import '@fontsource/inter/600.css';
@import '@fontsource/inter/700.css';
@import '@fontsource/inter/800.css';

@import '@fontsource/nunito-sans/400.css';
@import '@fontsource/nunito-sans/500.css';
@import '@fontsource/nunito-sans/600.css';
@import '@fontsource/nunito-sans/700.css';
@import '@fontsource/nunito-sans/800.css';

@import '@fontsource/poppins/400.css';
@import '@fontsource/poppins/500.css';
@import '@fontsource/poppins/600.css';
@import '@fontsource/poppins/700.css';
@import '@fontsource/poppins/800.css';

/** **************************************
* Plugins
*************************************** */
/* scrollbar */
@import './components/scrollbar/styles.css';

/** **************************************
* Baseline
*************************************** */
html {
  height: 100%;
  -webkit-overflow-scrolling: touch;
}
body,
#root,
#root__layout {
  display: flex;
  flex: 1 1 auto;
  min-height: 100%;
  flex-direction: column;
}
img {
  max-width: 100%;
  vertical-align: middle;
}
ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
input[type='number'] {
  -moz-appearance: textfield;
  appearance: none;
}
input[type='number']::-webkit-outer-spin-button {
  margin: 0;
  -webkit-appearance: none;
}
input[type='number']::-webkit-inner-spin-button {
  margin: 0;
  -webkit-appearance: none;
}

/** **************************************
* Dark Mode Gradient Overlay
*************************************** */
[data-mui-color-scheme='dark'] #root__layout::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(30, 30, 30, 1) 0%,
    rgba(255, 111, 60, 0.05) 90%,
    rgba(255, 111, 60, 0.1) 100%
  );
  pointer-events: none;
  z-index: 0;
  will-change: transform;
}

/* Ensure all direct children of layout have higher z-index than the gradient overlay */
#root__layout > * {
  position: relative;
  z-index: 1;
}

/** **************************************
* Custom Cursor Styles (Removed)
*************************************** */
/* Custom cursor styles have been removed to use the default browser cursor */
