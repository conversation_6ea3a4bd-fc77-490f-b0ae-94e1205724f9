import { Helmet } from 'react-helmet-async';
import { useParams, useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { Container, Alert } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { paths } from 'src/routes/paths';
import { AppContainerFixed } from 'src/components/common/app-container-fixed';
import TeamTemplateForm from 'src/sections/teams/form/team-template-form';
import { TeamTemplateFormValues } from 'src/sections/teams/form/use-team-template-form';
import { MOCK_TEAMS } from 'src/sections/teams/view/teams-view';
import { TeamData } from 'src/sections/teams/view/use-teams-view';

// ----------------------------------------------------------------------

export default function UseTemplatePage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  // const { enqueueSnackbar } = useSnackbar(); // Uncomment if needed for notifications

  const [team, setTeam] = useState<TeamData | null>(null);
  const [error, setError] = useState('');

  // Fetch the team data based on the ID - no loading state
  useEffect(() => {
    try {
      // Find the team in the mock data immediately
      const foundTeam = MOCK_TEAMS.find((t) => t.id === id);

      if (foundTeam) {
        setTeam(foundTeam);
        setError('');
      } else {
        // If team not found, use a default team
        setTeam(MOCK_TEAMS[0]);
        setError('');
      }
    } catch (err) {
      // On error, use a default team
      setTeam(MOCK_TEAMS[0]);
      setError('');
    }
  }, [id]);

  // Handle form submission with smooth navigation
  const handleSubmit = (data: TeamTemplateFormValues) => {
    // Here you would implement the logic to create a new team from the template
    console.log('Creating new team from template:', data);

    // Clear any form-related data from session storage except teamName
    try {
      sessionStorage.removeItem('formState');
      sessionStorage.removeItem('activeStep');
      // Clear any other form-related data that might be stored
    } catch (error) {
      console.error('Error clearing session storage:', error);
    }

    // In a real app, you would create the team and get the ID from the API response
    // For now, we'll generate a mock ID
    const newTeamId = `team_${Date.now()}`;

    // Store the team name in session storage so we can retrieve it on the chat page
    sessionStorage.setItem('teamName', data.name);

    // Navigate directly to the chat page for the new team with smooth transition
    navigate(paths.dashboard.teams.chat.replace(':id', newTeamId), { replace: true });
  };

  // Handle cancel with smooth navigation
  const handleCancel = () => {
    // Clear any form-related data from session storage
    try {
      sessionStorage.removeItem('formState');
      sessionStorage.removeItem('activeStep');
      // Clear any other form-related data that might be stored
    } catch (error) {
      console.error('Error clearing session storage:', error);
    }

    // Use React Router navigation for smooth transition
    navigate(paths.dashboard.teams.root, { replace: true });
  };

  return (
    <>
      <Helmet>
        <title>{t('pages.teams.useTemplate')}</title>
      </Helmet>

      <AppContainerFixed>
        <Container maxWidth="lg">
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {/* Always render the form with a valid team */}
          <TeamTemplateForm
            team={team || MOCK_TEAMS[0]}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
          />
        </Container>
      </AppContainerFixed>
    </>
  );
}
