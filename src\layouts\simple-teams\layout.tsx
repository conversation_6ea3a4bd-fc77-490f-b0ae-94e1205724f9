import type { Theme, SxProps, Breakpoint } from '@mui/material/styles';

import Alert from '@mui/material/Alert';

import { useBoolean } from 'src/hooks/use-boolean';
import { useNavData } from '../config-nav-dashboard';

import { Main } from './main';
import { HeaderBase } from '../core/header-base';
import { LayoutSection } from '../core/layout-section';

// ----------------------------------------------------------------------

export type SimpleTeamsLayoutProps = {
  sx?: SxProps<Theme>;
  children: React.ReactNode;
};

export function SimpleTeamsLayout({ sx, children }: SimpleTeamsLayoutProps) {
  const mobileNavOpen = useBoolean();
  const navData = useNavData();

  const layoutQuery: Breakpoint = 'md';

  return (
    <LayoutSection
      /** **************************************
       * Header
       *************************************** */
      headerSection={
        <HeaderBase
          layoutQuery={layoutQuery}
          onOpenNav={mobileNavOpen.onTrue}
          data={{
            nav: navData,
          }}
          slotsDisplay={{
            signIn: false,
            account: true,
            purchase: false,
            contacts: false,
            searchbar: true,
            credits: true,
            workspaces: false,
            menuButton: false,
            localization: false,
            notifications: true,
            helpLink: false,
          }}
          slots={{
            topArea: (
              <Alert severity="info" sx={{ display: 'none', borderRadius: 0 }}>
                This is an info Alert.
              </Alert>
            ),
          }}
          slotProps={{ container: { maxWidth: false } }}
        />
      }
      /** **************************************
       * Footer
       *************************************** */
      footerSection={null}
      /** **************************************
       * Style
       *************************************** */
      sx={sx}
    >
      <Main>{children}</Main>
    </LayoutSection>
  );
}
